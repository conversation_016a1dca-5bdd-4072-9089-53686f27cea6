**方案编号**: 5
**开始日期**: 2025-01-27
**完成日期**: 进行中
**状态**: 🚧 进行中
**负责人**: AI Assistant
**上级方案**: [0-总](0-总.md)
**前置依赖**: [3-订单系统](3-订单系统.md), [4-用户认证系统](4-用户认证系统.md)

## 方案概述

将发货功能整合到订单系统中，使发货成为订单管理的核心功能而非独立业务模块。整合现有的ShippingService到OrderService中，提供统一的订单+发货管理体验。

## 方案修正说明

**原方案问题**: 将发货功能设计为独立的物流系统
**修正方案**: 发货功能应该是订单系统的组成部分
**核心思路**: 将ShippingService的功能整合到OrderService中，保持业务逻辑的内聚性

## 技术方案

### 后端架构
- **Spring Boot**: 发货API服务
- **MyBatis**: 发货数据持久化
- **MySQL**: 发货信息存储
- **JWT**: 权限控制（管理员发货权限）

### 前端架构
- **Next.js**: 用户端发货进度查看
- **Ant Design Pro**: 管理端发货操作界面
- **Zustand**: 发货状态管理
- **TypeScript**: 类型安全

## 实施计划

### 阶段1：后端OrderService整合
- [ ] 扩展OrderService接口，添加发货相关方法
- [ ] 将ShippingService核心逻辑迁移到OrderServiceImpl
- [ ] 扩展OrderController，添加发货相关端点
- [ ] 保持现有数据库表结构和关联关系

### 阶段2：前端OrderStore整合
- [ ] 扩展OrderStore，添加发货相关状态和方法
- [ ] 更新订单详情页面，集成发货信息展示
- [ ] 更新订单管理页面，添加发货操作功能
- [ ] 实现发货状态筛选和管理功能

### 阶段3：权限和安全控制
- [ ] 实现发货操作的ADMIN权限控制
- [ ] 添加发货操作的审计日志
- [ ] 确保用户只能查看自己订单的发货信息
- [ ] 实现发货状态的业务逻辑验证

### 阶段4：系统集成测试
- [ ] 测试订单+发货的完整业务流程
- [ ] 验证权限控制的有效性
- [ ] 测试前后端数据同步
- [ ] 用户体验和界面集成测试

## 实现详情

### 1. 后端OrderService整合

#### OrderService接口扩展
```java
public interface OrderService {
    // 现有方法...

    // 新增发货相关方法
    ShippingInfo shipOrder(Long orderId, ShippingRequest request);
    ShippingInfo getOrderShipping(Long orderId);
    ShippingInfo updateOrderShipping(Long orderId, ShippingRequest request);
    List<Order> getOrdersReadyToShip();
}
```

#### OrderController发货端点
- `POST /api/orders/{id}/ship` - 发货操作（整合到订单API）
- `GET /api/orders/{id}/shipping` - 获取订单发货信息
- `PUT /api/orders/{id}/shipping` - 更新订单发货信息
- `GET /api/orders/ready-to-ship` - 获取待发货订单列表

### 2. 前端OrderStore整合

#### OrderStore状态扩展
```typescript
interface OrderState {
  // 现有状态...

  // 新增发货相关方法
  shipOrder: (orderId: number, shippingData: ShippingRequest) => Promise<ShippingInfo>;
  getOrderShipping: (orderId: number) => ShippingInfo | null;
  updateOrderShipping: (orderId: number, shippingData: ShippingRequest) => Promise<ShippingInfo>;
  getOrdersReadyToShip: () => Order[];
}
```

#### 订单详情页面整合
- 在订单详情中直接显示发货信息
- 发货状态时间轴展示
- 物流跟踪信息集成
- 管理员发货操作按钮

#### 订单管理页面整合
- 发货状态筛选器
- 待发货订单突出显示
- 批量发货操作（管理员）
- 发货信息快速录入

### 3. 业务逻辑整合

#### 发货操作流程（在订单系统内）
1. 在订单列表中选择待发货订单
2. 点击"发货"按钮（集成在订单操作中）
3. 录入物流信息（快递公司、运单号）
4. 确认发货操作
5. 自动更新订单状态为"已发货"
6. 在订单详情中显示发货信息

## 数据库设计

### 保持现有表结构
- **shipping_info表**: 已存在，保持现有结构
- **orders表**: 已存在，通过order_id关联发货信息
- **order_statuses表**: 已存在，包含SHIPPED等发货相关状态

### 现有表关联关系
```sql
-- 发货信息表（已存在）
shipping_info.order_id -> orders.id

-- 订单状态表（已存在）
orders.status_id -> order_statuses.id
```

### 无需新建表
由于发货功能整合到订单系统，现有的数据库结构已经足够支持所需功能，无需创建额外的表。

## 业务逻辑整合

### 订单状态流转（包含发货）
1. **PENDING** (待处理) → **PAID** (已付款)
2. **PAID** (已付款) → **PROCESSING** (处理中)
3. **PROCESSING** (处理中) → **SHIPPED** (已发货) ← 发货操作触发
4. **SHIPPED** (已发货) → **DELIVERED** (已送达)

### 发货权限控制（在订单系统内）
- 只有ADMIN角色可以执行发货操作
- 只能对PAID或PROCESSING状态的订单进行发货
- 发货操作直接更新订单状态为SHIPPED
- 发货后可以更新物流信息，但不能撤销发货状态

### 整合优势
- 发货成为订单生命周期的自然组成部分
- 统一的订单+发货管理界面
- 简化业务逻辑和数据流
- 更好的用户体验和管理效率

## 文件结构调整

```
后端 (ecommerce-backend):
├── src/main/java/io/github/roshad/ecommerce/
│   ├── order/
│   │   ├── OrderService.java (扩展发货方法)
│   │   ├── OrderServiceImpl.java (整合发货逻辑)
│   │   ├── OrderController.java (添加发货端点)
│   │   └── Order.java (可选：添加发货信息关联)
│   └── shipping/
│       ├── ShippingInfo.java (保持现有)
│       ├── ShippingMapper.java (保持现有)
│       ├── ShippingRequest.java (保持现有)
│       └── ShippingService.java (逻辑迁移到OrderService)

前端用戶端 (mall-client):
├── src/
│   ├── store/
│   │   └── orderStore.ts (扩展发货功能)
│   ├── types/
│   │   └── order.ts (包含发货类型定义)
│   ├── components/
│   │   └── Order/
│   │       ├── OrderShipping.tsx (发货信息组件)
│   │       ├── ShippingTimeline.tsx (发货时间轴)
│   │       └── ShippingActions.tsx (管理员发货操作)
│   └── app/orders/
│       ├── page.tsx (整合发货筛选)
│       └── [id]/page.tsx (整合发货信息展示)
```

## 当前进度

### 已完成 ✅
- 基础订单系统
- 用户认证系统
- 订单状态管理
- 独立的ShippingService（待整合）

### 已完成 ✅
- OrderService发货功能整合
- OrderController发货端点添加
- OrderStore发货状态管理
- 订单详情页面发货信息展示
- 发货权限控制（ADMIN角色）
- 发货模态框组件
- 管理员订单管理页面发货按钮集成
- 用户端订单列表管理员发货功能
- Git仓库初始化和代码提交

### 待开始 ⏳
- 发货操作审计日志
- 批量发货功能
- 发货通知系统
- 实际物流API集成

## 整合效果预期

### 用户体验改善
- 在订单详情中直接查看发货信息
- 统一的订单管理界面
- 更直观的订单状态流转

### 管理效率提升
- 在订单列表中直接发货操作
- 减少系统间跳转
- 统一的订单+发货数据视图

### 技术架构优化
- 减少服务间依赖
- 简化业务逻辑
- 提高代码内聚性

---

**最后更新**: 2025-01-27
**下次检查**: 2025-01-28
