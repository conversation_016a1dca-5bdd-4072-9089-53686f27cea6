// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 此处后端没有提供注释 GET /api/products */
export async function getAllProducts(options?: { [key: string]: any }) {
  return request<API.Product[]>('/api/products', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 POST /api/products */
export async function createProduct(body: API.Product, options?: { [key: string]: any }) {
  return request<string>('/api/products', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 GET /api/products/${param0} */
export async function getProductById(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getProductByIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Product>(`/api/products/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 PUT /api/products/${param0} */
export async function updateProduct(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.updateProductParams,
  body: API.Product,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<string>(`/api/products/${param0}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 DELETE /api/products/${param0} */
export async function deleteProduct(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteProductParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<string>(`/api/products/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 GET /api/products/search */
export async function searchProducts(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.searchProductsParams,
  options?: { [key: string]: any },
) {
  return request<API.Product[]>('/api/products/search', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
