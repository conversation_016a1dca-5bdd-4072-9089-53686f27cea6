package io.github.roshad.ecommerce.order;

import org.apache.ibatis.annotations.*;
import java.util.List;

@Mapper
public interface OrderMapper {

    @Select("SELECT o.*, u.username as user_name FROM orders o " +
            "LEFT JOIN users u ON o.user_id = u.id WHERE o.id = #{id}")
    @Results(id = "OrderResultMap", value = {
        @Result(column = "id", property = "id", id = true),
        @Result(column = "user_id", property = "userId"),
        @Result(column = "user_name", property = "userName"),
        @Result(column = "order_code", property = "orderCode"),
        @Result(column = "total_amount", property = "totalAmount"),
        @Result(column = "shipping_address_line1", property = "shippingAddressLine1"),
        @Result(column = "shipping_address_line2", property = "shippingAddressLine2"),
        @Result(column = "shipping_city", property = "shippingCity"),
        @Result(column = "shipping_postal_code", property = "shippingPostalCode"),
        @Result(column = "shipping_country", property = "shippingCountry"),
        @Result(column = "payment_method", property = "paymentMethod"),
        @Result(column = "payment_status", property = "paymentStatus"),
        @Result(column = "notes", property = "notes"),
        @Result(column = "status_id", property = "statusId"),
        @Result(column = "created_at", property = "createdAt"),
        @Result(column = "updated_at", property = "updatedAt"),
        @Result(property = "orderItems", column = "id", javaType = List.class,
                many = @Many(select = "io.github.roshad.ecommerce.order.OrderMapper.findOrderItemsByOrderId")),
        @Result(property = "status", column = "status_id", javaType = OrderStatus.class,
                one = @One(select = "io.github.roshad.ecommerce.order.OrderStatusMapper.findById"))
    })
    Order findById(@Param("id") Long id);

    @Select("SELECT id, order_id, product_id, quantity, price_at_purchase, product_name_at_purchase FROM order_items WHERE order_id = #{orderId}")
    @Results({
        @Result(column = "id", property = "id", id = true),
        @Result(column = "order_id", property = "orderId"),
        @Result(column = "product_id", property = "productId"),
        @Result(column = "quantity", property = "quantity"),
        @Result(column = "price_at_purchase", property = "priceAtPurchase"),
        @Result(column = "product_name_at_purchase", property = "productNameAtPurchase")
    })
    List<OrderItem> findOrderItemsByOrderId(@Param("orderId") Long orderId);

    @Insert("INSERT INTO orders (user_id, order_code, total_amount, status_id, shipping_address_line1, shipping_address_line2, shipping_city, shipping_postal_code, shipping_country, payment_method, payment_status, notes, created_at, updated_at) " +
            "VALUES (#{userId}, #{orderCode}, #{totalAmount}, #{statusId}, #{shippingAddressLine1}, #{shippingAddressLine2}, #{shippingCity}, #{shippingPostalCode}, #{shippingCountry}, #{paymentMethod}, #{paymentStatus}, #{notes}, #{createdAt}, #{updatedAt})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insert(Order order);

    @Select("SELECT o.*, u.username as user_name FROM orders o " +
            "LEFT JOIN users u ON o.user_id = u.id WHERE o.user_id = #{userId}")
    @ResultMap("OrderResultMap")
    List<Order> findByUserId(@Param("userId") Long userId);

    @Update("UPDATE orders SET user_id = #{userId}, order_code = #{orderCode}, total_amount = #{totalAmount}, status_id = #{statusId}, " +
            "shipping_address_line1 = #{shippingAddressLine1}, shipping_address_line2 = #{shippingAddressLine2}, " +
            "shipping_city = #{shippingCity}, shipping_postal_code = #{shippingPostalCode}, shipping_country = #{shippingCountry}, " +
            "payment_method = #{paymentMethod}, payment_status = #{paymentStatus}, notes = #{notes}, updated_at = #{updatedAt} " +
            "WHERE id = #{id}")
    void update(Order order);

    @Select("SELECT o.*, u.username as user_name FROM orders o " +
            "LEFT JOIN users u ON o.user_id = u.id")
    @ResultMap("OrderResultMap")
    List<Order> findAll();

    @Delete("DELETE FROM orders WHERE id = #{id}")
    void deleteById(@Param("id") Long id);
}