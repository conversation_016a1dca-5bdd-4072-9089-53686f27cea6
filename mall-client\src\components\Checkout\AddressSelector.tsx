'use client';

import React, { useState } from 'react';
import { PlusIcon, CheckIcon, PencilIcon, TrashIcon } from '@heroicons/react/24/outline';
import { useAddressStore } from '../../store/addressStore';
import { Address } from '../../types/order';

interface AddressSelectorProps {
  onAddressSelect?: (address: Address) => void;
}

const AddressSelector: React.FC<AddressSelectorProps> = ({ onAddressSelect }) => {
  const {
    addresses,
    selectedAddressId,
    selectAddress,
    setDefaultAddress,
    removeAddress
  } = useAddressStore();
  
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingAddress, setEditingAddress] = useState<Address | null>(null);

  const handleSelectAddress = (address: Address) => {
    selectAddress(address.id!);
    onAddressSelect?.(address);
  };

  const handleSetDefault = (addressId: number) => {
    setDefaultAddress(addressId);
  };

  const handleDelete = (addressId: number) => {
    if (confirm('確定要刪除這個地址嗎？')) {
      removeAddress(addressId);
    }
  };

  const selectedAddress = addresses.find(addr => addr.id === selectedAddressId);

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">收貨地址</h3>
        <button
          onClick={() => setShowAddForm(true)}
          className="flex items-center text-blue-600 hover:text-blue-800 text-sm font-medium"
        >
          <PlusIcon className="w-4 h-4 mr-1" />
          新增地址
        </button>
      </div>

      {addresses.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-gray-500 mb-4">您還沒有收貨地址</p>
          <button
            onClick={() => setShowAddForm(true)}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
          >
            添加收貨地址
          </button>
        </div>
      ) : (
        <div className="space-y-3">
          {addresses.map((address) => (
            <div
              key={address.id}
              className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                selectedAddressId === address.id
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => handleSelectAddress(address)}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="font-medium text-gray-900">
                      {address.recipientName}
                    </span>
                    <span className="text-gray-600">{address.phone}</span>
                    {address.isDefault && (
                      <span className="bg-red-100 text-red-600 text-xs px-2 py-1 rounded">
                        默認
                      </span>
                    )}
                  </div>
                  <p className="text-gray-600 text-sm">
                    {address.province} {address.city} {address.district} {address.detailAddress}
                  </p>
                  {address.postalCode && (
                    <p className="text-gray-500 text-xs mt-1">
                      郵編: {address.postalCode}
                    </p>
                  )}
                </div>
                
                <div className="flex items-center space-x-2 ml-4">
                  {selectedAddressId === address.id && (
                    <CheckIcon className="w-5 h-5 text-blue-600" />
                  )}
                  
                  <div className="flex space-x-1">
                    {!address.isDefault && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleSetDefault(address.id!);
                        }}
                        className="text-gray-400 hover:text-blue-600 text-xs"
                      >
                        設為默認
                      </button>
                    )}
                    
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        setEditingAddress(address);
                      }}
                      className="text-gray-400 hover:text-blue-600"
                    >
                      <PencilIcon className="w-4 h-4" />
                    </button>
                    
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDelete(address.id!);
                      }}
                      className="text-gray-400 hover:text-red-600"
                    >
                      <TrashIcon className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* 這裡可以添加地址表單組件 */}
      {showAddForm && (
        <div className="mt-4 p-4 border border-gray-200 rounded-lg bg-gray-50">
          <p className="text-gray-600">地址表單組件將在下一步實現</p>
          <button
            onClick={() => setShowAddForm(false)}
            className="mt-2 text-blue-600 hover:text-blue-800 text-sm"
          >
            取消
          </button>
        </div>
      )}
    </div>
  );
};

export default AddressSelector;
