package io.github.roshad.ecommerce.order;

import org.apache.ibatis.annotations.*;
import java.util.List;

@Mapper
public interface OrderItemMapper {

    @Insert("INSERT INTO order_items (order_id, product_id, quantity, price_at_purchase, product_name_at_purchase) " +
            "VALUES (#{orderId}, #{productId}, #{quantity}, #{priceAtPurchase}, #{productNameAtPurchase})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insert(OrderItem orderItem);

    @Select("SELECT id, order_id, product_id, quantity, price_at_purchase, product_name_at_purchase " +
            "FROM order_items WHERE id = #{id}")
    @Results({
        @Result(column = "id", property = "id", id = true),
        @Result(column = "order_id", property = "orderId"),
        @Result(column = "product_id", property = "productId"),
        @Result(column = "quantity", property = "quantity"),
        @Result(column = "price_at_purchase", property = "priceAtPurchase"),
        @Result(column = "product_name_at_purchase", property = "productNameAtPurchase")
    })
    OrderItem findById(@Param("id") Long id);

    @Select("SELECT id, order_id, product_id, quantity, price_at_purchase, product_name_at_purchase " +
            "FROM order_items WHERE order_id = #{orderId}")
    @Results({
        @Result(column = "id", property = "id", id = true),
        @Result(column = "order_id", property = "orderId"),
        @Result(column = "product_id", property = "productId"),
        @Result(column = "quantity", property = "quantity"),
        @Result(column = "price_at_purchase", property = "priceAtPurchase"),
        @Result(column = "product_name_at_purchase", property = "productNameAtPurchase")
    })
    List<OrderItem> findByOrderId(@Param("orderId") Long orderId);

    @Update("UPDATE order_items SET product_id = #{productId}, quantity = #{quantity}, " +
            "price_at_purchase = #{priceAtPurchase}, product_name_at_purchase = #{productNameAtPurchase} " +
            "WHERE id = #{id}")
    void update(OrderItem orderItem);

    @Delete("DELETE FROM order_items WHERE id = #{id}")
    void deleteById(@Param("id") Long id);

    @Delete("DELETE FROM order_items WHERE order_id = #{orderId}")
    void deleteByOrderId(@Param("orderId") Long orderId);
}
