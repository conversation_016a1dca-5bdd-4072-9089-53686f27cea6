import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { Address } from '../types/order';

// 地址管理状态接口
interface AddressState {
  addresses: Address[];
  selectedAddressId: number | null;
  
  // 操作方法
  addAddress: (address: Omit<Address, 'id'>) => void;
  updateAddress: (id: number, address: Partial<Address>) => void;
  removeAddress: (id: number) => void;
  setDefaultAddress: (id: number) => void;
  selectAddress: (id: number) => void;
  getDefaultAddress: () => Address | null;
  getSelectedAddress: () => Address | null;
  clearAddresses: () => void;
}

// 生成临时ID（实际项目中应该由后端生成）
let tempId = 1;

// 创建地址管理 store
export const useAddressStore = create<AddressState>()(
  persist(
    (set, get) => ({
      addresses: [],
      selectedAddressId: null,

      // 添加地址
      addAddress: (address) => {
        const { addresses } = get();
        const newAddress: Address = {
          ...address,
          id: tempId++,
          // 如果是第一个地址，自动设为默认
          isDefault: addresses.length === 0 ? true : address.isDefault
        };

        // 如果新地址设为默认，取消其他地址的默认状态
        const updatedAddresses = address.isDefault
          ? addresses.map(addr => ({ ...addr, isDefault: false }))
          : addresses;

        set({
          addresses: [...updatedAddresses, newAddress],
          // 如果没有选中的地址，自动选中新添加的地址
          selectedAddressId: get().selectedAddressId || newAddress.id!
        });
      },

      // 更新地址
      updateAddress: (id, updatedAddress) => {
        const { addresses } = get();
        
        const newAddresses = addresses.map(address => {
          if (address.id === id) {
            const updated = { ...address, ...updatedAddress };
            
            // 如果设为默认地址，取消其他地址的默认状态
            if (updatedAddress.isDefault) {
              return updated;
            }
            return updated;
          }
          
          // 如果当前更新的地址设为默认，其他地址取消默认
          if (updatedAddress.isDefault) {
            return { ...address, isDefault: false };
          }
          
          return address;
        });

        set({ addresses: newAddresses });
      },

      // 删除地址
      removeAddress: (id) => {
        const { addresses, selectedAddressId } = get();
        const addressToRemove = addresses.find(addr => addr.id === id);
        const newAddresses = addresses.filter(address => address.id !== id);
        
        // 如果删除的是默认地址，设置第一个地址为默认
        if (addressToRemove?.isDefault && newAddresses.length > 0) {
          newAddresses[0].isDefault = true;
        }

        set({
          addresses: newAddresses,
          // 如果删除的是当前选中的地址，重置选中状态
          selectedAddressId: selectedAddressId === id
            ? (newAddresses.length > 0 ? newAddresses[0].id! : null)
            : selectedAddressId
        });
      },

      // 设置默认地址
      setDefaultAddress: (id) => {
        const { addresses } = get();
        const newAddresses = addresses.map(address => ({
          ...address,
          isDefault: address.id === id
        }));

        set({ addresses: newAddresses });
      },

      // 选择地址（用于结算）
      selectAddress: (id) => {
        set({ selectedAddressId: id });
      },

      // 获取默认地址
      getDefaultAddress: () => {
        const { addresses } = get();
        return addresses.find(address => address.isDefault) || null;
      },

      // 获取当前选中的地址
      getSelectedAddress: () => {
        const { addresses, selectedAddressId } = get();
        if (!selectedAddressId) return null;
        return addresses.find(address => address.id === selectedAddressId) || null;
      },

      // 清空所有地址
      clearAddresses: () => {
        set({
          addresses: [],
          selectedAddressId: null
        });
      }
    }),
    {
      name: 'address-storage',
      version: 1,
    }
  )
);
