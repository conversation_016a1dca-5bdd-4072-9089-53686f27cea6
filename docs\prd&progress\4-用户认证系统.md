# 4-用户认证系统

**方案编号**: 4
**开始日期**: 2025-01-27
**预计完成**: 2025-01-28
**状态**: 🔄 进行中 (60%)
**负责人**: AI Assistant
**上级方案**: [0-总](0-总.md)
**前置依赖**: [3-订单系统](3-订单系统.md)

## 方案概述

完善用户认证系统，解决当前的数据库结构问题，实现完整的用户注册、登录、权限管理功能，为个性化功能和数据同步奠定基础。

## 当前问题

### 已识别问题
- ❌ **数据库结构问题**: users表缺少role字段，导致用户注册失败
- ⚠️ **角色管理**: 需要实现更灵活的多角色管理系统
- ⚠️ **前端集成**: 用户状态管理和页面集成待完善

### 解决方案
- 更新数据库结构，采用roles表和user_roles关联表
- 重构后端用户管理代码
- 开发前端用户认证组件和页面

## 技术方案

### 数据库设计
- **roles表**: 角色定义（USER, ADMIN, SELLER, MODERATOR）
- **user_roles表**: 用户角色关联表（支持多角色）
- **users表**: 移除role字段，通过关联表管理角色

### 后端架构
- **Spring Security**: 身份验证和授权
- **JWT**: 无状态token认证
- **MyBatis**: 数据库操作
- **角色权限**: 基于角色的访问控制

### 前端架构
- **用户状态管理**: Zustand + 持久化
- **路由保护**: Next.js中间件
- **表单验证**: 客户端和服务端双重验证

## 实施计划

### 阶段1：数据库结构修复 ✅
- [x] 创建数据库检查脚本 (check_database_structure.sql)
- [x] 创建数据初始化脚本 (initialize_basic_data.sql)
- [x] 设计新的角色管理表结构

### 阶段2：后端代码重构 ✅
- [x] 创建Role实体类和RoleMapper
- [x] 更新User类支持多角色
- [x] 修改UserMapper移除role字段依赖
- [x] 更新UserServiceImpl处理角色分配
- [x] 更新AuthController返回角色信息

### 阶段3：前端用户系统开发 📋
- [ ] 创建用户状态管理 (userStore.ts)
- [ ] 开发登录页面 (/login)
- [ ] 开发注册页面 (/register)
- [ ] 开发用户个人中心 (/profile)
- [ ] 实现JWT token管理

### 阶段4：系统集成和测试 📋
- [ ] 路由保护中间件
- [ ] Header用户状态显示
- [ ] 登录状态持久化
- [ ] 权限控制集成
- [ ] 完整功能测试

## 已完成工作

### 1. 数据库结构设计 ✅

#### 新增表结构
```sql
-- 角色表
CREATE TABLE `roles` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `name` VARCHAR(50) NOT NULL UNIQUE,
  `description` TEXT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 用户角色关联表
CREATE TABLE `user_roles` (
  `user_id` BIGINT NOT NULL,
  `role_id` INT NOT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`user_id`, `role_id`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`),
  FOREIGN KEY (`role_id`) REFERENCES `roles`(`id`)
);
```

#### 基础数据
- USER, ADMIN, SELLER, MODERATOR 角色
- 为现有用户分配默认USER角色

### 2. 后端代码重构 ✅

#### Role实体类和Mapper
```java
public class Role {
    private Integer id;
    private String name;
    private String description;
    // getters, setters, 构造函数
}

@Mapper
public interface RoleMapper {
    List<Role> findRolesByUserId(Long userId);
    int assignRoleToUser(Long userId, Integer roleId);
    // 其他角色管理方法
}
```

#### User类更新
```java
public class User implements UserDetails {
    private List<Role> roles = new ArrayList<>();
    
    public boolean hasRole(String roleName) {
        return roles.stream().anyMatch(role -> role.getName().equals(roleName));
    }
    
    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        // 基于角色列表生成权限
    }
}
```

### 3. 创建的脚本文件 ✅
- **check_database_structure.sql**: 檢查數據庫結構
- **initialize_basic_data.sql**: 初始化角色和基礎數據
- **fix_users_table.sql**: 修复用户表结构（备用）

## 待完成工作

### 1. 前端用户状态管理
```typescript
interface UserState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  login: (credentials) => Promise<void>;
  logout: () => void;
  register: (userData) => Promise<void>;
  updateProfile: (data) => Promise<void>;
}
```

### 2. 用户认证页面
- **登录页面**: 用户名/密码登录，记住我功能
- **注册页面**: 用户注册表单，验证逻辑
- **个人中心**: 用户信息展示和编辑

### 3. 权限控制
- **路由保护**: 保护需要登录的页面
- **组件权限**: 基于角色显示/隐藏功能
- **API权限**: 请求拦截和权限验证

### 4. 用户体验
- **登录状态持久化**: 刷新页面保持登录
- **自动登录**: 记住我功能
- **登出处理**: 清理用户数据

## 技术实现细节

### JWT Token管理
```typescript
// Token存储和管理
const tokenManager = {
  setToken: (token: string) => localStorage.setItem('token', token),
  getToken: () => localStorage.getItem('token'),
  removeToken: () => localStorage.removeItem('token'),
  isTokenValid: (token: string) => {
    // JWT token验证逻辑
  }
};
```

### 路由保护中间件
```typescript
// Next.js中间件保护路由
export function middleware(request: NextRequest) {
  const token = request.cookies.get('token');
  const isAuthPage = request.nextUrl.pathname.startsWith('/auth');
  const isProtectedPage = protectedRoutes.includes(request.nextUrl.pathname);
  
  // 路由保护逻辑
}
```

## 测试计划

### 数据库测试
- [ ] 执行数据库结构检查
- [ ] 验证角色数据初始化
- [ ] 测试用户注册功能

### 后端API测试
- [ ] 用户注册API测试
- [ ] 用户登录API测试
- [ ] 角色权限验证测试
- [ ] JWT token验证测试

### 前端功能测试
- [ ] 登录页面功能测试
- [ ] 注册页面功能测试
- [ ] 用户状态管理测试
- [ ] 路由保护测试

### 集成测试
- [ ] 前后端登录流程测试
- [ ] 权限控制集成测试
- [ ] 用户体验完整性测试

## 风险评估

### 技术风险
- **中等**: 数据库结构变更可能影响现有数据
- **低**: JWT实现相对标准
- **中等**: 多角色权限控制复杂度

### 业务风险
- **低**: 用户认证是标准功能
- **中等**: 用户体验需要细致打磨

## 成功标准

### 功能标准
- [x] 数据库结构更新成功
- [x] 后端代码重构完成
- [ ] 用户注册功能正常
- [ ] 用户登录功能正常
- [ ] 权限控制有效
- [ ] 用户体验流畅

### 技术标准
- [ ] API响应时间 < 500ms
- [ ] 前端页面加载 < 2秒
- [ ] 错误处理完善
- [ ] 安全性验证通过

## 下一步行动

### 立即执行
1. **数据库更新**: 执行SQL脚本修复数据库结构
2. **后端测试**: 重启服务并测试用户注册
3. **问题修复**: 根据测试结果调整代码

### 后续开发
1. **前端开发**: 创建用户认证相关页面和组件
2. **系统集成**: 实现完整的认证流程
3. **测试验证**: 全面测试用户认证功能

---

**当前进度**: 60% (数据库和后端重构完成)
**下次更新**: 2025-01-28  
**下一步**: 执行数据库脚本并测试后端功能
