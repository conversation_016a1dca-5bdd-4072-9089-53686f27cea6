'use client';

import React, { useState, useEffect } from 'react';

interface Category {
  id: number;
  name: string;
  count?: number;
}

interface CategoryFilterProps {
  onCategoryChange: (categoryId: number | null) => void;
  selectedCategory: number | null;
}

const CategoryFilter: React.FC<CategoryFilterProps> = ({ 
  onCategoryChange, 
  selectedCategory 
}) => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);

  // 模拟分类数据，实际项目中会从API获取
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        // 这里应该调用后端API获取分类
        // const response = await fetch('http://localhost:8080/api/categories');
        // const data = await response.json();
        
        // 模拟数据
        const mockCategories: Category[] = [
          { id: 1, name: '电子产品', count: 15 },
          { id: 2, name: '服装配饰', count: 8 },
          { id: 3, name: '家居用品', count: 12 },
          { id: 4, name: '运动户外', count: 6 },
          { id: 5, name: '美妆护理', count: 9 },
        ];
        
        setCategories(mockCategories);
      } catch (error) {
        console.error('获取分类失败:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, []);

  if (loading) {
    return (
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-3">商品分类</h3>
        <div className="flex flex-wrap gap-2">
          {[1, 2, 3, 4, 5].map((i) => (
            <div key={i} className="h-8 w-20 bg-gray-200 rounded animate-pulse"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="mb-6">
      <h3 className="text-lg font-semibold mb-3">商品分类</h3>
      <div className="flex flex-wrap gap-2">
        <button
          onClick={() => onCategoryChange(null)}
          className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
            selectedCategory === null
              ? 'bg-blue-500 text-white'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
        >
          全部
        </button>
        {categories.map((category) => (
          <button
            key={category.id}
            onClick={() => onCategoryChange(category.id)}
            className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
              selectedCategory === category.id
                ? 'bg-blue-500 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            {category.name}
            {category.count && (
              <span className="ml-1 text-xs opacity-75">({category.count})</span>
            )}
          </button>
        ))}
      </div>
    </div>
  );
};

export default CategoryFilter;
