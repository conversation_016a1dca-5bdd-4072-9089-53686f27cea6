'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { UserIcon, EnvelopeIcon, CalendarIcon, ShieldCheckIcon } from '@heroicons/react/24/outline';
import { useAuthStore } from '../../store/authStore';
import toast from 'react-hot-toast';

export default function ProfilePage() {
  const { user, isAuthenticated, logout, checkAuth } = useAuthStore();
  const router = useRouter();

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login');
      return;
    }
    
    // 检查认证状态
    checkAuth();
  }, [isAuthenticated, router, checkAuth]);

  const handleLogout = () => {
    if (confirm('确定要登出吗？')) {
      logout();
      toast.success('已成功登出');
      router.push('/');
    }
  };

  if (!isAuthenticated || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">个人中心</h1>
          <p className="text-gray-600 mt-2">管理您的个人信息和账户设置</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 用户信息卡片 */}
          <div className="lg:col-span-2">
            <div className="bg-white shadow rounded-lg p-6">
              <div className="flex items-center space-x-4 mb-6">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                  <UserIcon className="w-8 h-8 text-blue-600" />
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-gray-900">{user.username}</h2>
                  <p className="text-gray-600">用户ID: {user.id}</p>
                </div>
              </div>

              <div className="space-y-4">
                {/* 用户名 */}
                <div className="flex items-center space-x-3">
                  <UserIcon className="w-5 h-5 text-gray-400" />
                  <div>
                    <p className="text-sm font-medium text-gray-500">用户名</p>
                    <p className="text-gray-900">{user.username}</p>
                  </div>
                </div>

                {/* 邮箱 */}
                <div className="flex items-center space-x-3">
                  <EnvelopeIcon className="w-5 h-5 text-gray-400" />
                  <div>
                    <p className="text-sm font-medium text-gray-500">邮箱</p>
                    <p className="text-gray-900">{user.email}</p>
                  </div>
                </div>

                {/* 角色 */}
                <div className="flex items-center space-x-3">
                  <ShieldCheckIcon className="w-5 h-5 text-gray-400" />
                  <div>
                    <p className="text-sm font-medium text-gray-500">角色</p>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      user.role === 'ADMIN' 
                        ? 'bg-red-100 text-red-800' 
                        : 'bg-green-100 text-green-800'
                    }`}>
                      {user.role === 'ADMIN' ? '管理员' : '普通用户'}
                    </span>
                  </div>
                </div>

                {/* 注册时间 */}
                <div className="flex items-center space-x-3">
                  <CalendarIcon className="w-5 h-5 text-gray-400" />
                  <div>
                    <p className="text-sm font-medium text-gray-500">注册时间</p>
                    <p className="text-gray-900">{formatDate(user.createdAt)}</p>
                  </div>
                </div>
              </div>

              {/* 操作按钮 */}
              <div className="mt-6 pt-6 border-t border-gray-200">
                <div className="flex space-x-4">
                  <button
                    onClick={() => toast('编辑功能开发中...')}
                    className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
                  >
                    编辑个人信息
                  </button>
                  <button
                    onClick={() => toast('修改密码功能开发中...')}
                    className="flex-1 bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700 transition-colors"
                  >
                    修改密码
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* 侧边栏 */}
          <div className="space-y-6">
            {/* 快速操作 */}
            <div className="bg-white shadow rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">快速操作</h3>
              <div className="space-y-3">
                <button
                  onClick={() => router.push('/cart')}
                  className="w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-50 rounded-md transition-colors"
                >
                  查看购物车
                </button>
                <button
                  onClick={() => toast('订单历史功能开发中...')}
                  className="w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-50 rounded-md transition-colors"
                >
                  订单历史
                </button>
                <button
                  onClick={() => toast('收货地址功能开发中...')}
                  className="w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-50 rounded-md transition-colors"
                >
                  收货地址
                </button>
                <button
                  onClick={() => toast('收藏夹功能开发中...')}
                  className="w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-50 rounded-md transition-colors"
                >
                  我的收藏
                </button>
              </div>
            </div>

            {/* 账户安全 */}
            <div className="bg-white shadow rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">账户安全</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">登录状态</span>
                  <span className="text-sm text-green-600 font-medium">已登录</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">账户安全</span>
                  <span className="text-sm text-green-600 font-medium">正常</span>
                </div>
              </div>
              
              <button
                onClick={handleLogout}
                className="w-full mt-4 bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700 transition-colors"
              >
                登出
              </button>
            </div>

            {/* 帮助与支持 */}
            <div className="bg-white shadow rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">帮助与支持</h3>
              <div className="space-y-3">
                <button
                  onClick={() => toast('帮助中心功能开发中...')}
                  className="w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-50 rounded-md transition-colors"
                >
                  帮助中心
                </button>
                <button
                  onClick={() => toast('联系客服功能开发中...')}
                  className="w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-50 rounded-md transition-colors"
                >
                  联系客服
                </button>
                <button
                  onClick={() => toast('意见反馈功能开发中...')}
                  className="w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-50 rounded-md transition-colors"
                >
                  意见反馈
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
