package io.github.roshad.ecommerce.order;

import io.github.roshad.ecommerce.shipping.ShippingInfo;
import io.github.roshad.ecommerce.shipping.ShippingRequest;
import java.util.List;

public interface OrderService {
    // 原有订单管理方法
    Order createOrder(Order order);
    Order getOrderById(Long id);
    List<Order> getOrdersByUserId(Long userId);
    void updateOrderStatus(Long orderId, String statusName);
    void cancelOrder(Long orderId);
    void deleteOrder(Long orderId);
    boolean isOrderOwner(Long orderId, String username);
    List<Order> getAllOrders();

    // 新增发货相关方法
    /**
     * 发货订单
     * @param orderId 订单ID
     * @param shippingRequest 发货请求信息
     * @return 发货信息
     */
    ShippingInfo shipOrder(Long orderId, ShippingRequest shippingRequest);

    /**
     * 获取订单的发货信息
     * @param orderId 订单ID
     * @return 发货信息，如果未发货则返回null
     */
    ShippingInfo getOrderShipping(Long orderId);

    /**
     * 更新订单的发货信息
     * @param orderId 订单ID
     * @param shippingRequest 发货请求信息
     * @return 更新后的发货信息
     */
    ShippingInfo updateOrderShipping(Long orderId, ShippingRequest shippingRequest);

    /**
     * 获取可以发货的订单列表
     * @return 待发货订单列表
     */
    List<Order> getOrdersReadyToShip();

    /**
     * 检查订单是否可以发货
     * @param orderId 订单ID
     * @return 是否可以发货
     */
    boolean canShipOrder(Long orderId);
}