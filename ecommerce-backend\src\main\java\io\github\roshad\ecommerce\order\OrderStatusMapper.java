package io.github.roshad.ecommerce.order;

import org.apache.ibatis.annotations.*;
import java.util.List;

@Mapper
public interface OrderStatusMapper {
    
    @Select("SELECT id, status_name, description, created_at, updated_at FROM order_statuses WHERE id = #{id}")
    OrderStatus findById(Integer id);
    
    @Select("SELECT id, status_name, description, created_at, updated_at FROM order_statuses WHERE status_name = #{statusName}")
    OrderStatus findByStatusName(String statusName);
    
    @Select("SELECT id, status_name, description, created_at, updated_at FROM order_statuses")
    List<OrderStatus> findAll();
    
    @Insert("INSERT INTO order_statuses (status_name, description) VALUES (#{statusName}, #{description})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insert(OrderStatus orderStatus);
    
    @Update("UPDATE order_statuses SET status_name = #{statusName}, description = #{description} WHERE id = #{id}")
    void update(OrderStatus orderStatus);
    
    @Delete("DELETE FROM order_statuses WHERE id = #{id}")
    void delete(Integer id);
}
