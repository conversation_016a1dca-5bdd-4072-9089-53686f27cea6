package io.github.roshad.ecommerce.shipping;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShippingRequest {
    
    @NotBlank(message = "运单号不能为空")
    private String trackingNumber;

    @NotBlank(message = "快递公司不能为空")
    private String carrier;

    @NotBlank(message = "配送方式不能为空")
    private String shippingMethod;

    private String notes;

    // 预计送达天数（用于计算预计送达时间）
    private Integer estimatedDays;
}
