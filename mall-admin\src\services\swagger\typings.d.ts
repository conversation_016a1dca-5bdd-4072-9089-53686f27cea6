declare namespace API {
  type cancelOrderParams = {
    id: number;
  };

  type deleteOrderParams = {
    id: number;
  };

  type deleteProductParams = {
    id: number;
  };

  type getOrderByIdParams = {
    id: number;
  };

  type getOrdersByUserIdParams = {
    userId: number;
  };

  type getOrderShippingParams = {
    id: number;
  };

  type getProductByIdParams = {
    id: number;
  };

  type getUserByUsernameParams = {
    username: string;
  };

  type LoginRequest = {
    username?: string;
    password?: string;
  };

  type Order = {
    id?: number;
    userId?: number;
    userName?: string;
    orderCode?: string;
    totalAmount?: number;
    shippingAddressLine1?: string;
    shippingAddressLine2?: string;
    shippingCity?: string;
    shippingPostalCode?: string;
    shippingCountry?: string;
    paymentMethod?: string;
    paymentStatus?: string;
    notes?: string;
    orderItems?: OrderItem[];
    status?: OrderStatus;
    statusId?: number;
    createdAt?: string;
    updatedAt?: string;
  };

  type OrderItem = {
    id?: number;
    orderId?: number;
    productId?: number;
    quantity?: number;
    price?: number;
    priceAtPurchase?: number;
    productNameAtPurchase?: string;
    order?: Order;
  };

  type OrderStatus = {
    id?: number;
    statusName?: string;
    description?: string;
    createdAt?: string;
    updatedAt?: string;
  };

  type OrderStatusUpdateRequest = {
    statusName?: string;
  };

  type Product = {
    id?: number;
    name: string;
    description?: string;
    price: number;
    stock: number;
    categoryId?: number;
    imageUrl?: string;
    tags?: string;
    createdAt?: string;
    updatedAt?: string;
  };

  type RegisterRequest = {
    username: string;
    password: string;
    confirmPassword: string;
    email: string;
    passwordMatching?: boolean;
  };

  type searchProductsParams = {
    keyword: string;
  };

  type shipOrderParams = {
    id: number;
  };

  type ShippingInfo = {
    id?: number;
    orderId?: number;
    trackingNumber?: string;
    carrier?: string;
    shippingMethod?: string;
    shippedAt?: string;
    estimatedDelivery?: string;
    notes?: string;
    createdAt?: string;
    updatedAt?: string;
  };

  type ShippingRequest = {
    trackingNumber: string;
    carrier: string;
    shippingMethod: string;
    notes?: string;
    estimatedDays?: number;
  };

  type updateOrderShippingParams = {
    id: number;
  };

  type updateOrderStatusParams = {
    id: number;
  };

  type updateProductParams = {
    id: number;
  };
}
