package io.github.roshad.ecommerce.auth;

import io.github.roshad.ecommerce.order.OrderService;
import io.github.roshad.ecommerce.auth.UserService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.authorization.AuthorizationDecision;
import org.springframework.security.authorization.AuthorizationManager;
import org.springframework.security.web.access.intercept.RequestAuthorizationContext;
import org.springframework.web.cors.CorsConfigurationSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.function.Supplier;
//123
@Configuration
@EnableWebSecurity
public class SecurityConfig {

    private final JwtAuthenticationFilter jwtAuthenticationFilter;
    private final OrderService orderService;
    private final UserService userService;
    private final CorsConfigurationSource corsConfigurationSource;

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http
            .csrf(csrf -> csrf.disable())
            .cors(cors -> cors.configurationSource(corsConfigurationSource))
            .authorizeHttpRequests(authorize -> authorize
                .requestMatchers("/api/auth/login", "/api/auth/register").permitAll()
                .requestMatchers("/swagger-ui.html", "/swagger-ui/**", "/v3/api-docs/**", "/webjars/**", "/swagger-resources/**").permitAll()
                .requestMatchers("/api/products/**").permitAll() // 允许所有对 /api/products 路径的访问
                .requestMatchers(HttpMethod.OPTIONS, "/**").permitAll() // 允许所有OPTIONS请求（CORS预检）
                .requestMatchers(HttpMethod.POST, "/api/orders").hasAnyRole("USER", "CUSTOMER")
                .requestMatchers(HttpMethod.GET, "/api/orders/{id}").access(orderOwnershipAuthorizationManager())
                .requestMatchers(HttpMethod.GET, "/api/orders/user/{userId}").access(sameUserAuthorizationManager())
                .requestMatchers(HttpMethod.PATCH, "/api/orders/{id}/status").access(orderStatusUpdateAuthorizationManager())
                .requestMatchers(HttpMethod.PATCH, "/api/orders/{id}/cancel").access(orderDeletionAuthorizationManager())
                .requestMatchers(HttpMethod.DELETE, "/api/orders/{id}").access(orderDeletionAuthorizationManager())
                // 发货相关接口权限配置
                .requestMatchers(HttpMethod.POST, "/api/orders/{id}/ship").hasRole("ADMIN")
                .requestMatchers(HttpMethod.GET, "/api/orders/{id}/shipping").access(orderOwnershipAuthorizationManager())
                .requestMatchers(HttpMethod.PUT, "/api/orders/{id}/shipping").hasRole("ADMIN")
                .requestMatchers(HttpMethod.GET, "/api/orders/ready-to-ship").hasRole("ADMIN")
                .anyRequest().authenticated()
            )
            .addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);
        return http.build();
    }

    public SecurityConfig(JwtAuthenticationFilter jwtAuthenticationFilter,
                          OrderService orderService,
                          UserService userService,
                          CorsConfigurationSource corsConfigurationSource) {
        this.jwtAuthenticationFilter = jwtAuthenticationFilter;
        this.orderService = orderService;
        this.userService = userService;
        this.corsConfigurationSource = corsConfigurationSource;
    }

    @Bean
    public AuthorizationManager<RequestAuthorizationContext> orderOwnershipAuthorizationManager() {
        return (authentication, context) -> {
            String orderId = context.getVariables().get("id");
            if (orderId == null) {
                return new AuthorizationDecision(false);
            }

            String username = authentication.get().getName();

            // 检查是否是管理员
            boolean isAdmin = authentication.get().getAuthorities().stream()
                .anyMatch(authority -> authority.getAuthority().equals("ROLE_ADMIN"));

            if (isAdmin) {
                return new AuthorizationDecision(true);
            }

            // 如果不是管理员，检查是否是订单所有者
            boolean isOwner = orderService.isOrderOwner(Long.parseLong(orderId), username);
            return new AuthorizationDecision(isOwner);
        };
    }

    @Bean
    public AuthorizationManager<RequestAuthorizationContext> sameUserAuthorizationManager() {
        return (authentication, context) -> {
            String userId = context.getVariables().get("userId");
            if (userId == null) {
                return new AuthorizationDecision(false);
            }

            String username = authentication.get().getName();
            Long userIdFromToken = userService.getUserId(username);
            boolean isSameUser = userIdFromToken != null && userIdFromToken.equals(Long.parseLong(userId));
            return new AuthorizationDecision(isSameUser);
        };
    }

    @Bean
    public AuthorizationManager<RequestAuthorizationContext> orderDeletionAuthorizationManager() {
        return (authentication, context) -> {
            String orderId = context.getVariables().get("id");
            if (orderId == null) {
                return new AuthorizationDecision(false);
            }

            String username = authentication.get().getName();

            // 检查是否是管理员
            boolean isAdmin = authentication.get().getAuthorities().stream()
                .anyMatch(authority -> authority.getAuthority().equals("ROLE_ADMIN"));

            if (isAdmin) {
                return new AuthorizationDecision(true);
            }

            // 如果不是管理员，检查是否是订单所有者
            boolean isOwner = orderService.isOrderOwner(Long.parseLong(orderId), username);
            return new AuthorizationDecision(isOwner);
        };
    }

    @Bean
    public AuthorizationManager<RequestAuthorizationContext> orderStatusUpdateAuthorizationManager() {
        return (authentication, context) -> {
            String orderId = context.getVariables().get("id");
            if (orderId == null) {
                return new AuthorizationDecision(false);
            }

            String username = authentication.get().getName();

            // 检查是否是管理员 - 管理员可以更新任何订单状态
            boolean isAdmin = authentication.get().getAuthorities().stream()
                .anyMatch(authority -> authority.getAuthority().equals("ROLE_ADMIN"));

            if (isAdmin) {
                return new AuthorizationDecision(true);
            }

            // 普通用户只能更新自己的订单状态
            boolean isOwner = orderService.isOrderOwner(Long.parseLong(orderId), username);
            return new AuthorizationDecision(isOwner);
        };
    }
}