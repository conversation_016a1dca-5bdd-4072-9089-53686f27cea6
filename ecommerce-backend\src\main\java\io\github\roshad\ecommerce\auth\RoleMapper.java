package io.github.roshad.ecommerce.auth;

import org.apache.ibatis.annotations.*;
import java.util.List;

@Mapper
public interface RoleMapper {

    @Select("SELECT * FROM roles WHERE id = #{id}")
    Role findById(@Param("id") Integer id);

    @Select("SELECT * FROM roles WHERE name = #{name}")
    Role findByName(@Param("name") String name);

    @Select("SELECT * FROM roles")
    List<Role> findAll();

    @Insert("INSERT INTO roles (name, description) VALUES (#{name}, #{description})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(Role role);

    @Update("UPDATE roles SET name = #{name}, description = #{description}, updated_at = CURRENT_TIMESTAMP WHERE id = #{id}")
    int update(Role role);

    @Delete("DELETE FROM roles WHERE id = #{id}")
    int deleteById(@Param("id") Integer id);

    // 獲取用戶的所有角色
    @Select("SELECT r.* FROM roles r " +
            "JOIN user_roles ur ON r.id = ur.role_id " +
            "WHERE ur.user_id = #{userId}")
    List<Role> findRolesByUserId(@Param("userId") Long userId);

    // 為用戶分配角色
    @Insert("INSERT INTO user_roles (user_id, role_id) VALUES (#{userId}, #{roleId})")
    int assignRoleToUser(@Param("userId") Long userId, @Param("roleId") Integer roleId);

    // 移除用戶角色
    @Delete("DELETE FROM user_roles WHERE user_id = #{userId} AND role_id = #{roleId}")
    int removeRoleFromUser(@Param("userId") Long userId, @Param("roleId") Integer roleId);

    // 移除用戶的所有角色
    @Delete("DELETE FROM user_roles WHERE user_id = #{userId}")
    int removeAllRolesFromUser(@Param("userId") Long userId);

    // 檢查用戶是否有特定角色
    @Select("SELECT COUNT(*) FROM user_roles WHERE user_id = #{userId} AND role_id = #{roleId}")
    int checkUserHasRole(@Param("userId") Long userId, @Param("roleId") Integer roleId);

    // 獲取擁有特定角色的所有用戶
    @Select("SELECT u.* FROM users u " +
            "JOIN user_roles ur ON u.id = ur.user_id " +
            "WHERE ur.role_id = #{roleId}")
    List<User> findUsersByRoleId(@Param("roleId") Integer roleId);
}
