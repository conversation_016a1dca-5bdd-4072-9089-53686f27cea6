package io.github.roshad.ecommerce.shipping;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShippingInfo {
    private Long id;
    private Long orderId;
    private String trackingNumber;
    private String carrier;
    private String shippingMethod;
    private LocalDateTime shippedAt;
    private LocalDateTime estimatedDelivery;
    private String notes;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // 常用快递公司常量
    public static final String CARRIER_SF = "顺丰速运";
    public static final String CARRIER_YTO = "圆通速递";
    public static final String CARRIER_ZTO = "中通快递";
    public static final String CARRIER_STO = "申通快递";
    public static final String CARRIER_EMS = "中国邮政EMS";
    public static final String CARRIER_JD = "京东物流";

    // 配送方式常量
    public static final String METHOD_STANDARD = "标准配送";
    public static final String METHOD_EXPRESS = "加急配送";
    public static final String METHOD_SAME_DAY = "当日达";
    public static final String METHOD_NEXT_DAY = "次日达";
}
