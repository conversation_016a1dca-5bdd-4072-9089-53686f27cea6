-- 创建发货信息表
-- 请在MySQL数据库中执行此SQL语句

USE mall;

CREATE TABLE shipping_info (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    order_id BIGINT NOT NULL,
    tracking_number VARCHAR(100) NOT NULL COMMENT '运单号',
    carrier VARCHAR(50) NOT NULL COMMENT '快递公司',
    shipping_method VARCHAR(50) NOT NULL COMMENT '配送方式',
    shipped_at DATETIME NOT NULL COMMENT '发货时间',
    estimated_delivery DATETIME COMMENT '预计送达时间',
    notes TEXT COMMENT '发货备注',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- 外键约束
    CONSTRAINT fk_shipping_order FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    
    -- 索引
    INDEX idx_order_id (order_id),
    INDEX idx_tracking_number (tracking_number),
    INDEX idx_shipped_at (shipped_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='发货信息表';

-- 验证表是否创建成功
DESCRIBE shipping_info;
