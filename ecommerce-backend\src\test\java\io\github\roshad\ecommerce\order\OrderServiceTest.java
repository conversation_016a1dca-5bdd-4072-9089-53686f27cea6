package io.github.roshad.ecommerce.order;

import io.github.roshad.ecommerce.order.Order;
import io.github.roshad.ecommerce.order.OrderMapper;
import io.github.roshad.ecommerce.order.OrderServiceImpl;
import io.github.roshad.ecommerce.order.OrderStatus;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.doAnswer;

@ExtendWith(MockitoExtension.class)
class OrderServiceTest {
    @Mock
    private OrderMapper orderMapper;

    @Mock
    private OrderStatusService orderStatusService;

    @Mock
    private io.github.roshad.ecommerce.auth.UserService userService;

    @InjectMocks
    private OrderServiceImpl orderService;

    @Test
    void createOrder_Success() {
        Order order = new Order();
        OrderStatus pendingStatus = new OrderStatus();
        pendingStatus.setId(1);
        pendingStatus.setStatusName(OrderStatus.PENDING);

        when(orderStatusService.findByStatusName(OrderStatus.PENDING)).thenReturn(pendingStatus);
        doAnswer(inv -> {
            Order o = inv.getArgument(0);
            o.setId(1L);
            return null;
        }).when(orderMapper).insert(any(Order.class));

        Order result = orderService.createOrder(order);

        assertNotNull(result.getId());
        assertEquals(pendingStatus, result.getStatus());
        assertEquals(pendingStatus.getId(), result.getStatusId());
        assertNotNull(result.getCreatedAt());
    }
    
    // 其他服务方法测试
}