# Mall 电商系统

一个完整的电商系统，包含用户购物端、管理后台和后端API服务。

## 项目结构

```
mall/
├── ecommerce-backend/     # Spring Boot 后端服务
├── mall-client/          # Next.js 用户购物端
├── mall-admin/           # 管理后台（计划中）
└── docs/                 # 项目文档
    └── progress/         # 开发进度文档
```

## 技术栈

### 后端 (ecommerce-backend)
- **框架**: Spring Boot 3.x
- **数据库**: MySQL 8.0
- **ORM**: MyBatis
- **认证**: JWT + Spring Security
- **构建工具**: Maven
- **部署**: Docker

### 前端 (mall-client)
- **框架**: Next.js 15
- **语言**: TypeScript
- **样式**: Tailwind CSS
- **状态管理**: Zustand
- **UI组件**: Heroicons
- **HTTP客户端**: Fetch API

### 管理后台 (mall-admin)
- **框架**: Ant Design Pro + UmiJS (计划中)
- **状态管理**: Dva (计划中)

## 功能特性

### ✅ 已完成功能
- **商品展示模块**: 商品列表、详情、搜索、分类
- **购物车系统**: 添加商品、数量管理、价格计算
- **订单系统**: 订单创建、状态管理、地址管理
- **用户认证系统**: 注册、登录、JWT认证
- **订单发货功能**: 发货管理、物流跟踪、状态更新

### 🚧 开发中功能
- 用户个人中心
- 支付系统集成

### 📋 计划功能
- 管理后台完善
- 数据统计分析
- 性能优化
- 部署上线

## 快速开始

### 环境要求
- Node.js 18+
- Java 17+
- MySQL 8.0+
- Maven 3.6+

### 后端启动

1. 进入后端目录
```bash
cd ecommerce-backend
```

2. 配置数据库
```bash
# 创建数据库
mysql -u root -p
CREATE DATABASE ecommerce_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

3. 修改配置文件
```bash
# 编辑 src/main/resources/application.yml
# 配置数据库连接信息
```

4. 启动应用
```bash
mvn spring-boot:run
```

后端服务将在 http://localhost:8080 启动

### 前端启动

1. 进入前端目录
```bash
cd mall-client
```

2. 安装依赖
```bash
npm install
```

3. 启动开发服务器
```bash
npm run dev
```

前端应用将在 http://localhost:3000 启动

## 数据库设计

### 核心表结构
- `users` - 用户表
- `user_roles` - 用户角色关联表
- `roles` - 角色表
- `products` - 商品表
- `categories` - 分类表
- `orders` - 订单表
- `order_items` - 订单项表
- `order_statuses` - 订单状态表
- `shipping_info` - 发货信息表
- `addresses` - 地址表

## API文档

### 认证相关
- `POST /api/auth/register` - 用户注册
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/refresh` - 刷新Token

### 商品相关
- `GET /api/products` - 获取商品列表
- `GET /api/products/{id}` - 获取商品详情
- `GET /api/categories` - 获取分类列表

### 订单相关
- `POST /api/orders` - 创建订单
- `GET /api/orders/{id}` - 获取订单详情
- `GET /api/orders/user/{userId}` - 获取用户订单
- `PATCH /api/orders/{id}/status` - 更新订单状态

### 发货相关
- `POST /api/orders/{id}/ship` - 发货订单
- `GET /api/orders/{id}/shipping` - 获取发货信息
- `PUT /api/orders/{id}/shipping` - 更新发货信息
- `GET /api/orders/ready-to-ship` - 获取待发货订单

## 开发进度

详细的开发进度请查看 [docs/progress/](./docs/progress/) 目录下的文档。

## 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式

如有问题或建议，请通过以下方式联系：
- 创建 Issue
- 发送邮件

---

**最后更新**: 2025-01-27
