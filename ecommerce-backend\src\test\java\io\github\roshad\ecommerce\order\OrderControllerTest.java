package io.github.roshad.ecommerce.order;

import io.github.roshad.ecommerce.auth.JwtUtil;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.servlet.MockMvc;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ExtendWith(MockitoExtension.class)
@WebMvcTest(controllers = OrderController.class, excludeAutoConfiguration = {
    org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration.class
})
class OrderControllerTest {
    @Autowired
    private MockMvc mockMvc;
    
    @Mock
    private OrderService orderService;

    @Mock
    private OrderStatusService orderStatusService;

    @Mock
    private JwtUtil jwtUtil;

    @Mock
    private io.github.roshad.ecommerce.auth.UserService userService;

    @InjectMocks
    private OrderController orderController;

    @Test
    @WithMockUser(roles = "USER")
    void createOrder_AuthenticatedUser_Success() throws Exception {
        Order order = new Order();
        when(orderService.createOrder(any())).thenReturn(order);
        
        mockMvc.perform(post("/api/orders")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{}"))
                .andExpect(status().isCreated());
    }
    
    // 其他控制器端点测试
}

