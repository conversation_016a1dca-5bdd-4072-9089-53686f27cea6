// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 此处后端没有提供注释 POST /api/auth/login */
export async function login(body: API.LoginRequest, options?: { [key: string]: any }) {
  return request<Record<string, any>>('/api/auth/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 GET /api/auth/profile */
export async function getCurrentUserProfile(options?: { [key: string]: any }) {
  return request<Record<string, any>>('/api/auth/profile', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 POST /api/auth/register */
export async function register(body: API.RegisterRequest, options?: { [key: string]: any }) {
  return request<Record<string, any>>('/api/auth/register', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 GET /api/auth/user/${param0} */
export async function getUserByUsername(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getUserByUsernameParams,
  options?: { [key: string]: any },
) {
  const { username: param0, ...queryParams } = params;
  return request<Record<string, any>>(`/api/auth/user/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}
