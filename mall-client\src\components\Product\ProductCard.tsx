import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import AddToCartButton from '../Cart/AddToCartButton';

interface Product {
  id: number;
  name: string;
  description: string;
  price: number;
  stock: number;
  imageUrl: string;
}

interface ProductCardProps {
  product: Product;
}

const ProductCard: React.FC<ProductCardProps> = ({ product }) => {
  // 处理图片URL，如果是相对路径或无效URL，使用占位图
  const getImageUrl = (url: string) => {
    if (!url) return '/placeholder.jpg';
    
    // 如果是相对路径，转换为绝对路径
    if (url.startsWith('/images/')) {
      return `https://via.placeholder.com/300x200/f0f0f0/666666?text=${encodeURIComponent(product.name)}`;
    }
    
    // 如果是完整URL但可能无效，提供备用
    try {
      new URL(url);
      return url;
    } catch {
      return `https://via.placeholder.com/300x200/f0f0f0/666666?text=${encodeURIComponent(product.name)}`;
    }
  };

  return (
    <Link href={`/products/${product.id}`}>
      <div className="border rounded-lg shadow-md p-4 flex flex-col items-center text-center hover:shadow-lg transition-shadow duration-300 cursor-pointer">
        <div className="relative w-full h-48 mb-4">
          <Image
            src={getImageUrl(product.imageUrl)}
            alt={product.name}
            fill
            style={{ objectFit: "contain" }}
            className="rounded-md"
            onError={(e) => {
              // 如果图片加载失败，使用占位图
              const target = e.target as HTMLImageElement;
              target.src = '/placeholder.jpg';
            }}
          />
        </div>
        <h2 className="text-lg font-semibold mb-2 line-clamp-2">{product.name}</h2>
        <p className="text-gray-600 mb-2 text-sm line-clamp-3">{product.description}</p>
        <p className="text-xl font-bold text-green-600 mb-1">¥{product.price.toFixed(2)}</p>
        <p className="text-sm text-gray-500">
          库存: {product.stock > 0 ? product.stock : '缺货'}
        </p>
        {product.stock === 0 && (
          <span className="mt-2 px-2 py-1 bg-red-100 text-red-600 text-xs rounded">
            暂时缺货
          </span>
        )}

        {/* 加入购物车按钮 */}
        <div className="mt-3 w-full" onClick={(e) => e.preventDefault()}>
          <AddToCartButton
            product={{
              id: product.id,
              name: product.name,
              price: product.price,
              stock: product.stock,
              imageUrl: product.imageUrl,
            }}
            size="sm"
            className="w-full"
          />
        </div>
      </div>
    </Link>
  );
};

export default ProductCard;
