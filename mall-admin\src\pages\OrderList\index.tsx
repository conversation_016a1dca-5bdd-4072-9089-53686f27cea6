import { PageContainer, ProTable, ProColumns } from '@ant-design/pro-components';
import { message, Popconfirm, Button, Modal, Form, Input, Select } from 'antd';
import React, { useRef, useState } from 'react';
import API from '@/services/ant-design-pro/api';
import { history } from '@umijs/max';

const OrderList: React.FC = () => {
  const actionRef = useRef<any>();
  const [shippingModalVisible, setShippingModalVisible] = useState(false);
  const [currentOrder, setCurrentOrder] = useState<API.Order | null>(null);
  const [form] = Form.useForm();
  const [showReadyToShip, setShowReadyToShip] = useState(false);

  const handleRemove = async (id: number) => {
    try {
      const token = localStorage.getItem('token');
      console.log('删除订单 - Token:', token ? '存在' : '不存在');
      console.log('删除订单 - ID:', id);

      if (!token) {
        message.error('未登录或登录已过期，请重新登录');
        return;
      }

      // 调用真正的删除API
      const response = await fetch(`http://localhost:8080/api/orders/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      console.log('删除订单 - 响应状态:', response.status);

      if (response.status === 403) {
        message.error('权限不足，请确认您有管理员权限');
        return;
      }

      if (response.status === 401) {
        message.error('登录已过期，请重新登录');
        return;
      }

      if (!response.ok) {
        const errorText = await response.text();
        console.error('删除订单 - 错误响应:', errorText);
        throw new Error(`删除请求失败: ${response.status}`);
      }

      message.success('订单删除成功');
      actionRef.current?.reload();
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败，请重试');
    }
  };

  // 取消订单
  const handleCancelOrder = async (id: number) => {
    try {
      const token = localStorage.getItem('token');
      console.log('取消订单 - Token:', token ? '存在' : '不存在');
      console.log('取消订单 - ID:', id);

      if (!token) {
        message.error('未登录或登录已过期，请重新登录');
        return;
      }

      const response = await fetch(`http://localhost:8080/api/orders/${id}/cancel`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      console.log('取消订单 - 响应状态:', response.status);

      if (response.status === 403) {
        message.error('权限不足，请确认您有管理员权限');
        return;
      }

      if (response.status === 401) {
        message.error('登录已过期，请重新登录');
        return;
      }

      if (!response.ok) {
        const errorText = await response.text();
        console.error('取消订单 - 错误响应:', errorText);
        throw new Error(`取消请求失败: ${response.status}`);
      }

      message.success('订单取消成功');
      actionRef.current?.reload();
    } catch (error) {
      console.error('取消失败:', error);
      message.error('取消失败，请重试');
    }
  };

  // 检查订单是否可以发货
  const canShipOrder = (order: API.Order) => {
    const status = order.status?.statusName;
    return status === 'PAID' || status === 'PROCESSING';
  };

  // 打开发货模态框
  const handleShipOrder = (order: API.Order) => {
    setCurrentOrder(order);
    setShippingModalVisible(true);
    form.resetFields();
  };

  // 处理发货提交
  const handleShippingSubmit = async (values: any) => {
    if (!currentOrder) return;

    try {
      // 调用发货API
      const response = await fetch(`http://localhost:8080/api/orders/${currentOrder.id}/ship`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify({
          trackingNumber: values.trackingNumber,
          carrier: values.carrier,
          shippingMethod: values.shippingMethod,
          notes: values.notes,
          estimatedDays: values.estimatedDays,
        }),
      });

      if (!response.ok) {
        throw new Error('发货请求失败');
      }

      const shippingInfo = await response.json();
      console.log('发货成功:', shippingInfo);

      message.success('发货成功！');
      setShippingModalVisible(false);
      setCurrentOrder(null);
      actionRef.current?.reload();
    } catch (error) {
      console.error('发货失败:', error);
      message.error('发货失败，请重试');
    }
  };

  const columns: ProColumns<API.Order>[] = [
    {
      title: '订单ID',
      dataIndex: 'id',
      valueType: 'text',
      search: false,
      render: (text, record) => (
        <a onClick={() => history.push(`/order/detail/${record.id}`)}>{text}</a>
      ),
    },
    {
      title: '用户名',
      dataIndex: 'userName',
      valueType: 'text',
      search: false,
    },
    {
      title: '总价',
      dataIndex: 'totalAmount',
      valueType: 'money',
      search: false,
    },
    {
      title: '状态',
      dataIndex: ['status', 'statusName'],
      valueType: 'text',
      search: false,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      valueType: 'dateTime',
      search: false,
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      valueType: 'dateTime',
      search: false,
    },
    {
      title: '商品详情',
      dataIndex: 'products',
      valueType: 'text',
      search: false,
      render: (_, record) => (
        <div>
          {record.products?.map((product, index) => (
            <div key={index}>
              {product.name} - {product.price} x {product.quantity}
            </div>
          ))}
        </div>
      ),
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      render: (_: any, record: API.Order) => {
        const actions = [];

        // 发货按钮
        if (canShipOrder(record)) {
          actions.push(
            <Button
              key="ship"
              type="primary"
              size="small"
              onClick={() => handleShipOrder(record)}
            >
              发货
            </Button>
          );
        }

        // 取消订单按钮（只有未取消的订单才显示）
        if (record.status?.statusName !== 'CANCELLED') {
          actions.push(
            <Popconfirm
              key="cancel"
              title="确定取消该订单吗？"
              onConfirm={() => handleCancelOrder(record.id!)}
              okText="是"
              cancelText="否"
            >
              <Button type="link" size="small">
                取消订单
              </Button>
            </Popconfirm>
          );
        }

        // 删除按钮（管理员可以删除任何订单）
        actions.push(
          <Popconfirm
            key="delete"
            title="确定删除该订单吗？此操作不可恢复！"
            onConfirm={() => handleRemove(record.id!)}
            okText="是"
            cancelText="否"
          >
            <Button type="link" danger size="small">
              删除
            </Button>
          </Popconfirm>
        );

        return actions;
      },
    },
  ];



  return (
    <PageContainer>
      <ProTable<API.Order>
        headerTitle="订单列表"
        actionRef={actionRef}
        rowKey="id"
        request={async (params) => {
          let allOrders = await API.Order.getAllOrders(); // 获取所有订单数据

          // 如果启用了待发货筛选，只显示可以发货的订单
          if (showReadyToShip) {
            allOrders = allOrders.filter((order: API.Order) => canShipOrder(order));
          }

          const { current, pageSize } = params;
          const startIndex = (current! - 1) * pageSize!;
          const endIndex = startIndex + pageSize!;
          const paginatedData = allOrders.slice(startIndex, endIndex);

          return {
            data: paginatedData,
            success: true,
            total: allOrders.length,
          };
        }}
        columns={columns}
        pagination={{
          defaultPageSize: 10, // 设置默认分页大小
          showSizeChanger: true,
        }}
        toolBarRender={() => [
          <Button
            key="ready-to-ship"
            type={showReadyToShip ? 'primary' : 'default'}
            onClick={() => {
              setShowReadyToShip(!showReadyToShip);
              actionRef.current?.reload();
            }}
          >
            {showReadyToShip ? '显示全部订单' : '只显示待发货订单'}
          </Button>,
        ]}
      />

      {/* 发货模态框 */}
      <Modal
        title="订单发货"
        open={shippingModalVisible}
        onCancel={() => {
          setShippingModalVisible(false);
          setCurrentOrder(null);
        }}
        onOk={() => form.submit()}
        okText="确认发货"
        cancelText="取消"
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleShippingSubmit}
        >
          <Form.Item label="订单信息">
            <div style={{ padding: '8px', backgroundColor: '#f5f5f5', borderRadius: '4px' }}>
              <p><strong>订单号:</strong> {currentOrder?.id}</p>
              <p><strong>用户:</strong> {currentOrder?.userName}</p>
              <p><strong>金额:</strong> ¥{currentOrder?.totalAmount}</p>
            </div>
          </Form.Item>

          <Form.Item
            name="trackingNumber"
            label="运单号"
            rules={[{ required: true, message: '请输入运单号' }]}
          >
            <Input placeholder="请输入运单号" />
          </Form.Item>

          <Form.Item
            name="carrier"
            label="快递公司"
            rules={[{ required: true, message: '请选择快递公司' }]}
          >
            <Select placeholder="请选择快递公司">
              <Select.Option value="顺丰速运">顺丰速运</Select.Option>
              <Select.Option value="圆通速递">圆通速递</Select.Option>
              <Select.Option value="中通快递">中通快递</Select.Option>
              <Select.Option value="申通快递">申通快递</Select.Option>
              <Select.Option value="中国邮政EMS">中国邮政EMS</Select.Option>
              <Select.Option value="京东物流">京东物流</Select.Option>
              <Select.Option value="韵达速递">韵达速递</Select.Option>
              <Select.Option value="百世快递">百世快递</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="shippingMethod"
            label="配送方式"
            rules={[{ required: true, message: '请选择配送方式' }]}
          >
            <Select placeholder="请选择配送方式">
              <Select.Option value="标准配送">标准配送</Select.Option>
              <Select.Option value="加急配送">加急配送</Select.Option>
              <Select.Option value="当日达">当日达</Select.Option>
              <Select.Option value="次日达">次日达</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="estimatedDays"
            label="预计送达天数"
          >
            <Select placeholder="请选择预计送达天数">
              <Select.Option value={1}>1天</Select.Option>
              <Select.Option value={2}>2天</Select.Option>
              <Select.Option value={3}>3天</Select.Option>
              <Select.Option value={5}>5天</Select.Option>
              <Select.Option value={7}>7天</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="notes"
            label="备注"
          >
            <Input.TextArea
              placeholder="请输入发货备注（可选）"
              rows={3}
            />
          </Form.Item>
        </Form>
      </Modal>
    </PageContainer>
  );
};

export default OrderList;
