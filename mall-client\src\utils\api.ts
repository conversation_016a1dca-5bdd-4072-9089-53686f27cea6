// API 配置和工具函数
import { CreateOrderRequest, PaymentMethod, Address } from '../types/order';

const API_BASE_URL = 'http://localhost:8080/api';

// 认证相关类型
interface LoginRequest {
  username: string;
  password: string;
}

interface LoginResponse {
  success: boolean;
  message: string;
  token?: string;
  user?: {
    id: number;
    username: string;
    email: string;
  };
}

// 简单的token存储
let authToken: string | null = null;

// 设置认证token
export const setAuthToken = (token: string) => {
  authToken = token;
  if (typeof window !== 'undefined') {
    localStorage.setItem('auth_token', token);
  }
};

// 获取认证token
export const getAuthToken = (): string | null => {
  if (authToken) return authToken;

  // 尝试从authStore获取token
  if (typeof window !== 'undefined') {
    try {
      const authStorage = localStorage.getItem('auth-storage');
      if (authStorage) {
        const parsed = JSON.parse(authStorage);
        if (parsed.state && parsed.state.token) {
          authToken = parsed.state.token;
          return authToken;
        }
      }
    } catch (error) {
      console.error('Failed to get token from auth storage:', error);
    }

    // 备用方案：从旧的存储获取
    authToken = localStorage.getItem('auth_token');
  }

  return authToken;
};

// 清除认证token
export const clearAuthToken = () => {
  authToken = null;
  if (typeof window !== 'undefined') {
    localStorage.removeItem('auth_token');
  }
};

// API 响应类型
interface ApiResponse<T> {
  data?: T;
  message?: string;
  success?: boolean;
}

// 后端订单数据结构
interface BackendOrderRequest {
  userId: number;
  orderCode: string;
  totalAmount: number;
  shippingAddressLine1: string;
  shippingAddressLine2?: string;
  shippingCity: string;
  shippingPostalCode?: string;
  shippingCountry: string;
  paymentMethod: string;
  paymentStatus: string;
  notes?: string;
  orderItems: {
    productId: number;
    quantity: number;
    priceAtPurchase: number;
    productNameAtPurchase: string;
  }[];
}

// 通用 API 请求函数
async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {},
  requireAuth: boolean = false
): Promise<T> {
  const url = `${API_BASE_URL}${endpoint}`;

  const defaultHeaders: Record<string, string> = {
    'Content-Type': 'application/json',
  };

  // 如果需要认证，添加Authorization头
  if (requireAuth) {
    const token = getAuthToken();
    if (token) {
      defaultHeaders['Authorization'] = `Bearer ${token}`;
    } else {
      throw new Error('未找到认证token，请先登录');
    }
  }

  const defaultOptions: RequestInit = {
    headers: defaultHeaders,
    mode: 'cors',
  };

  const config = {
    ...defaultOptions,
    ...options,
    headers: {
      ...defaultHeaders,
      ...(options.headers || {})
    }
  };

  try {
    const response = await fetch(url, config);

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('认证失败，请重新登录');
      }
      if (response.status === 403) {
        throw new Error('权限不足');
      }
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error(`API request failed for ${endpoint}:`, error);
    throw error;
  }
}

// 生成订单号
const generateOrderNumber = (): string => {
  const timestamp = Date.now();
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `ORD${timestamp}${random}`;
};

// 获取商品信息（用于订单创建）
async function getProductInfo(productId: number) {
  try {
    const product = await apiRequest(`/products/${productId}`);
    return product;
  } catch (error) {
    console.error(`Failed to get product ${productId}:`, error);
    // 返回默认值
    return {
      id: productId,
      name: `商品 ${productId}`,
      price: 99.99
    };
  }
}

// 获取当前用户ID
function getCurrentUserId(): number {
  if (typeof window !== 'undefined') {
    try {
      const authStorage = localStorage.getItem('auth-storage');
      if (authStorage) {
        const parsed = JSON.parse(authStorage);
        if (parsed.state && parsed.state.user && parsed.state.user.id) {
          return parsed.state.user.id;
        }
      }
    } catch (error) {
      console.error('Failed to get current user ID:', error);
    }
  }
  throw new Error('用户未登录或无法获取用户信息');
}

// 安全获取当前用户ID（不抛出错误）
function getCurrentUserIdSafe(): number | null {
  if (typeof window !== 'undefined') {
    try {
      const authStorage = localStorage.getItem('auth-storage');
      if (authStorage) {
        const parsed = JSON.parse(authStorage);
        if (parsed.state && parsed.state.user && parsed.state.user.id) {
          return parsed.state.user.id;
        }
      }
    } catch (error) {
      console.error('Failed to get current user ID:', error);
    }
  }
  return null;
}

// 转换前端订单请求为后端格式
async function transformOrderRequest(
  orderData: CreateOrderRequest,
  shippingAddress: Address,
  userId?: number // 可选参数，如果不提供则从认证状态获取
): Promise<BackendOrderRequest> {
  const actualUserId = userId || getCurrentUserId();
  const orderCode = generateOrderNumber();

  // 获取商品信息并计算价格
  const orderItems = await Promise.all(
    orderData.items.map(async (item) => {
      const product = await getProductInfo(item.productId);
      return {
        productId: item.productId,
        quantity: item.quantity,
        priceAtPurchase: product.price || 99.99,
        productNameAtPurchase: product.name || `商品 ${item.productId}`
      };
    })
  );

  // 计算总金额
  const subtotal = orderItems.reduce((sum, item) =>
    sum + (item.priceAtPurchase * item.quantity), 0
  );
  const shippingFee = subtotal >= 99 ? 0 : 10;
  const totalAmount = subtotal + shippingFee;

  return {
    userId: actualUserId,
    orderCode,
    totalAmount,
    shippingAddressLine1: `${shippingAddress.detailAddress}`,
    shippingAddressLine2: `${shippingAddress.recipientName} ${shippingAddress.phone}`,
    shippingCity: `${shippingAddress.province} ${shippingAddress.city} ${shippingAddress.district}`,
    shippingPostalCode: shippingAddress.postalCode || '',
    shippingCountry: '中国',
    paymentMethod: orderData.paymentMethod,
    paymentStatus: 'PENDING',
    notes: orderData.notes,
    orderItems
  };
}

// 认证相关 API
export const authApi = {
  // 登录
  login: async (username: string, password: string): Promise<LoginResponse> => {
    const response = await apiRequest<LoginResponse>('/auth/login', {
      method: 'POST',
      body: JSON.stringify({ username, password }),
    });

    // 如果登录成功，保存token
    if (response.success && response.token) {
      setAuthToken(response.token);
    }

    return response;
  },

  // 登出
  logout: () => {
    clearAuthToken();
  },

  // 检查是否已登录
  isLoggedIn: (): boolean => {
    return getAuthToken() !== null;
  },
};

// 订单相关 API
export const orderApi = {
  // 创建订单
  create: async (orderData: CreateOrderRequest, shippingAddress: Address) => {
    const backendOrderData = await transformOrderRequest(orderData, shippingAddress);
    return apiRequest('/orders', {
      method: 'POST',
      body: JSON.stringify(backendOrderData),
    }, true); // 需要认证
  },

  // 获取订单详情
  getById: async (id: number) => {
    return apiRequest(`/orders/${id}`, {}, true);
  },

  // 获取用户订单列表
  getByUserId: async (userId?: number) => {
    const actualUserId = userId || getCurrentUserId();
    return apiRequest(`/orders/user/${actualUserId}`, {}, true);
  },

  // 获取所有订单（管理员）
  getAll: async () => {
    return apiRequest('/orders', {}, true);
  },

  // 更新订单状态
  updateStatus: async (id: number, status: string) => {
    return apiRequest(`/orders/${id}/status`, {
      method: 'PATCH',
      body: JSON.stringify({ statusName: status }),
    }, true);
  },

  // 取消订单
  cancel: async (id: number) => {
    return apiRequest(`/orders/${id}`, {
      method: 'DELETE',
    }, true);
  },

  // 发货订单
  ship: async (id: number, shippingData: any) => {
    return apiRequest(`/orders/${id}/ship`, {
      method: 'POST',
      body: JSON.stringify(shippingData),
    }, true);
  },

  // 获取发货信息
  getShipping: async (id: number) => {
    return apiRequest(`/orders/${id}/shipping`, {}, true);
  },
};

// 商品相关 API
export const productApi = {
  // 获取商品列表
  getAll: async () => {
    return apiRequest('/products');
  },

  // 获取商品详情
  getById: async (id: number) => {
    return apiRequest(`/products/${id}`);
  },
};

export default {
  orderApi,
  productApi,
};

// 导出所有API和工具函数
export { orderApi, productApi, authApi, getCurrentUserId, getCurrentUserIdSafe };
