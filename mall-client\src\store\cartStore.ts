import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';

// 购物车商品接口
export interface CartItem {
  id: number;
  name: string;
  price: number;
  imageUrl: string;
  quantity: number;
  stock: number;
}

// 购物车状态接口
interface CartState {
  items: CartItem[];
  totalItems: number;
  totalPrice: number;
  isHydrated: boolean;
  
  // 操作方法
  addItem: (product: Omit<CartItem, 'quantity'>) => void;
  removeItem: (id: number) => void;
  updateQuantity: (id: number, quantity: number) => void;
  clearCart: () => void;
  getItemQuantity: (id: number) => number;
  setHydrated: () => void;
}

// 计算总数量
const calculateTotalItems = (items: CartItem[]): number => {
  return items.reduce((total, item) => total + item.quantity, 0);
};

// 计算总价格
const calculateTotalPrice = (items: CartItem[]): number => {
  return items.reduce((total, item) => total + (item.price * item.quantity), 0);
};

// 创建购物车 store
export const useCartStore = create<CartState>()(
  persist(
    (set, get) => ({
      items: [],
      totalItems: 0,
      totalPrice: 0,
      isHydrated: false,

      // 设置水合状态
      setHydrated: () => {
        set({ isHydrated: true });
      },

      // 添加商品到购物车
      addItem: (product) => {
        const { items } = get();
        const existingItem = items.find(item => item.id === product.id);

        let newItems: CartItem[];

        if (existingItem) {
          // 如果商品已存在，增加数量（不超过库存）
          const newQuantity = Math.min(existingItem.quantity + 1, product.stock);
          newItems = items.map(item =>
            item.id === product.id
              ? { ...item, quantity: newQuantity }
              : item
          );
        } else {
          // 如果是新商品，添加到购物车
          newItems = [...items, { ...product, quantity: 1 }];
        }

        const newTotalItems = calculateTotalItems(newItems);
        const newTotalPrice = calculateTotalPrice(newItems);

        set({
          items: newItems,
          totalItems: newTotalItems,
          totalPrice: newTotalPrice,
        });

        // 调试日志
        console.log('添加商品到购物车:', {
          product: product.name,
          newItems: newItems.length,
          totalItems: newTotalItems,
          totalPrice: newTotalPrice
        });
      },

      // 从购物车移除商品
      removeItem: (id) => {
        const { items } = get();
        const newItems = items.filter(item => item.id !== id);

        const newTotalItems = calculateTotalItems(newItems);
        const newTotalPrice = calculateTotalPrice(newItems);

        set({
          items: newItems,
          totalItems: newTotalItems,
          totalPrice: newTotalPrice,
        });

        console.log('移除商品:', { id, remainingItems: newItems.length });
      },

      // 更新商品数量
      updateQuantity: (id, quantity) => {
        const { items } = get();

        if (quantity <= 0) {
          // 如果数量为0或负数，移除商品
          get().removeItem(id);
          return;
        }

        const newItems = items.map(item => {
          if (item.id === id) {
            // 确保数量不超过库存
            const newQuantity = Math.min(quantity, item.stock);
            return { ...item, quantity: newQuantity };
          }
          return item;
        });

        const newTotalItems = calculateTotalItems(newItems);
        const newTotalPrice = calculateTotalPrice(newItems);

        set({
          items: newItems,
          totalItems: newTotalItems,
          totalPrice: newTotalPrice,
        });
      },

      // 清空购物车
      clearCart: () => {
        set({
          items: [],
          totalItems: 0,
          totalPrice: 0,
        });
        console.log('购物车已清空');
      },

      // 获取特定商品的数量
      getItemQuantity: (id) => {
        const { items } = get();
        const item = items.find(item => item.id === id);
        return item ? item.quantity : 0;
      },
    }),
    {
      name: 'cart-storage', // 本地存储的key
      storage: createJSONStorage(() => localStorage),
      // 可以选择性地存储某些字段
      partialize: (state) => ({
        items: state.items,
        totalItems: state.totalItems,
        totalPrice: state.totalPrice,
      }),
      // 水合完成后的回调
      onRehydrateStorage: () => (state) => {
        console.log('购物车数据已从本地存储恢复:', state);
        state?.setHydrated();
      },
    }
  )
);
