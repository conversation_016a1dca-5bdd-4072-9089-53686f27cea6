package io.github.roshad.ecommerce.config;

import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.util.ContentCachingRequestWrapper;
import org.springframework.web.util.ContentCachingResponseWrapper;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.Enumeration;

@Component
@Order(1)
public class RequestResponseLoggingFilter implements Filter {
    
    private static final Logger logger = LoggerFactory.getLogger(RequestResponseLoggingFilter.class);
    
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        
        // 只记录API请求
        if (!httpRequest.getRequestURI().startsWith("/api/")) {
            chain.doFilter(request, response);
            return;
        }
        
        // 包装请求和响应以便缓存内容
        ContentCachingRequestWrapper wrappedRequest = new ContentCachingRequestWrapper(httpRequest);
        ContentCachingResponseWrapper wrappedResponse = new ContentCachingResponseWrapper(httpResponse);
        
        try {
            // 记录请求
            logRequest(wrappedRequest);
            
            // 继续处理请求
            chain.doFilter(wrappedRequest, wrappedResponse);
            
            // 记录响应
            logResponse(wrappedRequest, wrappedResponse);
            
        } finally {
            // 将缓存的响应内容写回到原始响应
            wrappedResponse.copyBodyToResponse();
        }
    }
    
    private void logRequest(ContentCachingRequestWrapper request) {
        logger.info("=== 收到HTTP请求 ===");
        logger.info("请求方法: {}", request.getMethod());
        logger.info("请求URL: {}", request.getRequestURL().toString());
        logger.info("请求URI: {}", request.getRequestURI());
        logger.info("查询参数: {}", request.getQueryString());
        logger.info("客户端IP: {}", getClientIpAddress(request));
        
        // 记录请求头
        logger.info("请求头:");
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            String headerValue = request.getHeader(headerName);
            // 隐藏敏感信息
            if ("authorization".equalsIgnoreCase(headerName) && headerValue != null) {
                headerValue = maskSensitiveData(headerValue);
            }
            logger.info("  {}: {}", headerName, headerValue);
        }
        
        // 记录请求体
        byte[] content = request.getContentAsByteArray();
        if (content.length > 0) {
            try {
                String requestBody = new String(content, request.getCharacterEncoding());
                // 如果是登录请求，隐藏密码
                if (request.getRequestURI().contains("/auth/login")) {
                    requestBody = maskPasswordInLoginRequest(requestBody);
                }
                logger.info("请求体: {}", requestBody);
            } catch (UnsupportedEncodingException e) {
                logger.warn("无法解析请求体编码: {}", e.getMessage());
            }
        }
    }
    
    private void logResponse(ContentCachingRequestWrapper request, ContentCachingResponseWrapper response) {
        logger.info("=== HTTP响应 ===");
        logger.info("响应状态: {}", response.getStatus());
        logger.info("响应内容类型: {}", response.getContentType());
        
        // 记录响应头
        logger.info("响应头:");
        for (String headerName : response.getHeaderNames()) {
            logger.info("  {}: {}", headerName, response.getHeader(headerName));
        }
        
        // 记录响应体
        byte[] content = response.getContentAsByteArray();
        if (content.length > 0) {
            try {
                String responseBody = new String(content, response.getCharacterEncoding());
                // 如果是登录响应，隐藏token
                if (request.getRequestURI().contains("/auth/login")) {
                    responseBody = maskTokenInLoginResponse(responseBody);
                }
                logger.info("响应体: {}", responseBody);
            } catch (UnsupportedEncodingException e) {
                logger.warn("无法解析响应体编码: {}", e.getMessage());
            }
        }
        
        logger.info("=== 请求处理完成 ===\n");
    }
    
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
    
    private String maskSensitiveData(String data) {
        if (data == null || data.length() < 10) {
            return "***";
        }
        // 只显示前几个和后几个字符
        if (data.startsWith("Bearer ")) {
            String token = data.substring(7);
            if (token.length() > 6) {
                return "Bearer " + token.substring(0, 3) + "***" + token.substring(token.length() - 3);
            }
        }
        return "***";
    }
    
    private String maskPasswordInLoginRequest(String requestBody) {
        // 简单的密码隐藏，可以根据需要改进
        return requestBody.replaceAll("\"password\"\\s*:\\s*\"[^\"]*\"", "\"password\":\"***\"");
    }
    
    private String maskTokenInLoginResponse(String responseBody) {
        // 简单的token隐藏，可以根据需要改进
        return responseBody.replaceAll("\"token\"\\s*:\\s*\"[^\"]*\"", "\"token\":\"***\"");
    }
}
