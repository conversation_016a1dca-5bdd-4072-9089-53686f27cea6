package io.github.roshad.ecommerce.product;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Update;
import org.apache.ibatis.annotations.Delete;

import java.util.List;

@Mapper
public interface ProductMapper {

    @Select("SELECT id, name, description, price, stock, category_id, image_url, tags, created_at, updated_at FROM products")
    List<Product> findAllProducts();
    @Select("SELECT id, name, description, price, stock, category_id, image_url, tags, created_at, updated_at FROM products WHERE id = #{id}")
    Product findProductById(Long id);

    @Insert("INSERT INTO products(name, description, price, stock, category_id, image_url, tags) VALUES(#{name}, #{description}, #{price}, #{stock}, #{categoryId}, #{imageUrl}, #{tags})")
    int insertProduct(Product product);

    @Update("UPDATE products SET name=#{name}, description=#{description}, price=#{price}, stock=#{stock}, category_id=#{categoryId}, image_url=#{imageUrl}, tags=#{tags} WHERE id=#{id}")
    int updateProduct(Product product);

    @Delete("DELETE FROM products WHERE id=#{id}")
    int deleteProduct(Long id);

    @Select("SELECT id, name, description, price, stock, category_id, image_url, tags, created_at, updated_at FROM products WHERE name LIKE CONCAT('%', #{keyword}, '%') OR description LIKE CONCAT('%', #{keyword}, '%')")
    List<Product> searchProducts(String keyword);
}