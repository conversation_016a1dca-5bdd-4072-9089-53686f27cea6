import React from 'react';
import { PageContainer } from '@ant-design/pro-components';
import { Card, Descriptions, Spin, message, Button, Modal, Form, Input, Select, Space } from 'antd';
import { useParams } from '@umijs/max';
import { useEffect, useState } from 'react';
import API from '@/services/ant-design-pro/api';

const OrderDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [orderDetail, setOrderDetail] = useState<API.Order | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [shippingModalVisible, setShippingModalVisible] = useState<boolean>(false);
  const [shippingLoading, setShippingLoading] = useState<boolean>(false);
  const [form] = Form.useForm();

  useEffect(() => {
    const fetchOrderDetail = async () => {
      try {
        setLoading(true);
        const response = await API.Order.getOrderById({ id: parseInt(id!) });
        setOrderDetail(response);
      } catch (error) {
        message.error('获取订单详情失败');
      } finally {
        setLoading(false);
      }
    };
    if (id) {
      fetchOrderDetail();
    }
  }, [id]);

  // 处理发货
  const handleShipping = async (values: any) => {
    try {
      setShippingLoading(true);
      // 调用发货API
      const response = await fetch(`http://localhost:8080/api/orders/${id}/ship`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify({
          trackingNumber: values.trackingNumber,
          carrier: values.carrier,
          shippingMethod: values.shippingMethod,
          notes: values.notes,
          estimatedDays: values.estimatedDays,
        }),
      });

      if (!response.ok) {
        throw new Error('发货请求失败');
      }

      message.success('发货成功！');
      setShippingModalVisible(false);
      form.resetFields();
      // 重新获取订单详情
      const orderResponse = await API.Order.getOrderById({ id: parseInt(id!) });
      setOrderDetail(orderResponse);
    } catch (error) {
      console.error('发货失败:', error);
      message.error('发货失败，请重试');
    } finally {
      setShippingLoading(false);
    }
  };

  // 更新订单状态
  const updateOrderStatus = async (statusName: string) => {
    try {
      await API.Order.updateOrderStatus({
        id: parseInt(id!),
        statusName
      });
      message.success('状态更新成功！');
      // 重新获取订单详情
      const response = await API.Order.getOrderById({ id: parseInt(id!) });
      setOrderDetail(response);
    } catch (error) {
      message.error('状态更新失败，请重试');
    }
  };

  if (loading) {
    return <Spin tip="加载中..." />;
  }

  if (!orderDetail) {
    return <PageContainer>订单详情加载失败或订单不存在</PageContainer>;
  }

  return (
    <PageContainer header={{ title: `订单详情 - ${orderDetail.id}` }}>
      <Card
        title="基本信息"
        extra={
          <Space>
            {orderDetail.status?.statusName === 'PENDING' && (
              <Button type="primary" onClick={() => updateOrderStatus('PAID')}>
                标记为已付款
              </Button>
            )}
            {(orderDetail.status?.statusName === 'PAID' || orderDetail.status?.statusName === 'PROCESSING') && (
              <Button type="primary" onClick={() => setShippingModalVisible(true)}>
                发货
              </Button>
            )}
            {orderDetail.status?.statusName === 'SHIPPED' && (
              <Button onClick={() => updateOrderStatus('DELIVERED')}>
                标记为已送达
              </Button>
            )}
          </Space>
        }
      >
        <Descriptions column={2}>
          <Descriptions.Item label="订单ID">{orderDetail.id}</Descriptions.Item>
          <Descriptions.Item label="用户名">{orderDetail.userName}</Descriptions.Item>
          <Descriptions.Item label="总价">{orderDetail.totalAmount}</Descriptions.Item>
          <Descriptions.Item label="状态">{orderDetail.status?.statusName}</Descriptions.Item>
          <Descriptions.Item label="创建时间">{orderDetail.createdAt}</Descriptions.Item>
          <Descriptions.Item label="更新时间">{orderDetail.updatedAt}</Descriptions.Item>
        </Descriptions>
      </Card>
      <Card title="商品列表" style={{ marginTop: 16 }}>
        <Descriptions column={1}>
          {orderDetail.orderItems?.map((item, index) => (
            <Descriptions.Item key={index} label={`商品 ${index + 1}`}>
              {item.productNameAtPurchase} - 单价: {item.price} - 数量: {item.quantity}
            </Descriptions.Item>
          ))}
        </Descriptions>
      </Card>

      {/* 发货模态框 */}
      <Modal
        title="订单发货"
        open={shippingModalVisible}
        onCancel={() => {
          setShippingModalVisible(false);
          form.resetFields();
        }}
        onOk={() => form.submit()}
        okText="确认发货"
        cancelText="取消"
        confirmLoading={shippingLoading}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleShipping}
        >
          <Form.Item label="订单信息">
            <div style={{ padding: '8px', backgroundColor: '#f5f5f5', borderRadius: '4px' }}>
              <p><strong>订单号:</strong> {orderDetail?.id}</p>
              <p><strong>用户:</strong> {orderDetail?.userName}</p>
              <p><strong>金额:</strong> ¥{orderDetail?.totalAmount}</p>
            </div>
          </Form.Item>

          <Form.Item
            name="trackingNumber"
            label="运单号"
            rules={[{ required: true, message: '请输入运单号' }]}
          >
            <Input placeholder="请输入运单号" />
          </Form.Item>

          <Form.Item
            name="carrier"
            label="快递公司"
            rules={[{ required: true, message: '请选择快递公司' }]}
          >
            <Select placeholder="请选择快递公司">
              <Select.Option value="顺丰速运">顺丰速运</Select.Option>
              <Select.Option value="圆通速递">圆通速递</Select.Option>
              <Select.Option value="中通快递">中通快递</Select.Option>
              <Select.Option value="申通快递">申通快递</Select.Option>
              <Select.Option value="中国邮政EMS">中国邮政EMS</Select.Option>
              <Select.Option value="京东物流">京东物流</Select.Option>
              <Select.Option value="韵达速递">韵达速递</Select.Option>
              <Select.Option value="百世快递">百世快递</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="shippingMethod"
            label="配送方式"
            rules={[{ required: true, message: '请选择配送方式' }]}
          >
            <Select placeholder="请选择配送方式">
              <Select.Option value="标准配送">标准配送</Select.Option>
              <Select.Option value="加急配送">加急配送</Select.Option>
              <Select.Option value="当日达">当日达</Select.Option>
              <Select.Option value="次日达">次日达</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="estimatedDays"
            label="预计送达天数"
          >
            <Select placeholder="请选择预计送达天数">
              <Select.Option value={1}>1天</Select.Option>
              <Select.Option value={2}>2天</Select.Option>
              <Select.Option value={3}>3天</Select.Option>
              <Select.Option value={5}>5天</Select.Option>
              <Select.Option value={7}>7天</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="notes"
            label="备注"
          >
            <Input.TextArea
              placeholder="请输入发货备注（可选）"
              rows={3}
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* 可以在这里添加更多详情，如物流信息、支付信息等 */}
    </PageContainer>
  );
};

export default OrderDetail;
