'use client';

import React, { useState } from 'react';
import { ShoppingCartIcon, CheckIcon } from '@heroicons/react/24/outline';
import { useCartStore } from '../../store/cartStore';
import toast from 'react-hot-toast';

interface Product {
  id: number;
  name: string;
  price: number;
  stock: number;
  imageUrl: string;
}

interface AddToCartButtonProps {
  product: Product;
  quantity?: number;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'primary' | 'secondary';
}

const AddToCartButton: React.FC<AddToCartButtonProps> = ({
  product,
  quantity = 1,
  className = '',
  size = 'md',
  variant = 'primary'
}) => {
  const { addItem, getItemQuantity } = useCartStore();
  const [isAdding, setIsAdding] = useState(false);
  const [justAdded, setJustAdded] = useState(false);

  const currentQuantity = getItemQuantity(product.id);
  const canAddMore = currentQuantity + quantity <= product.stock;

  const handleAddToCart = async () => {
    if (!canAddMore) {
      toast.error(`库存不足，最多只能添加 ${product.stock} 件`);
      return;
    }

    setIsAdding(true);
    
    try {
      // 模拟添加延迟（可选）
      await new Promise(resolve => setTimeout(resolve, 300));
      
      for (let i = 0; i < quantity; i++) {
        addItem({
          id: product.id,
          name: product.name,
          price: product.price,
          imageUrl: product.imageUrl,
          stock: product.stock,
        });
      }

      // 顯示成功提示
      toast.success(`已添加 "${product.name}" 到购物车`, {
        duration: 2000,
        icon: '🛒',
      });

      // 顯示成功狀態
      setJustAdded(true);
      setTimeout(() => setJustAdded(false), 2000);

    } catch (error: unknown) {
      console.error("添加购物车失败:", error);
      toast.error('添加失败，请重试');
    } finally {
      setIsAdding(false);
    }
  };

  // 尺寸样式
  const sizeClasses = {
    sm: 'py-1 px-3 text-sm',
    md: 'py-2 px-4 text-base',
    lg: 'py-3 px-6 text-lg'
  };

  // 变体样式
  const variantClasses = {
    primary: 'bg-blue-600 text-white hover:bg-blue-700',
    secondary: 'bg-white text-blue-600 border border-blue-600 hover:bg-blue-50'
  };

  // 按钮状态样式
  const getButtonClasses = () => {
    const baseClasses = `
      inline-flex items-center justify-center
      font-semibold rounded-lg
      transition-all duration-200
      focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
      disabled:cursor-not-allowed
      ${sizeClasses[size]}
      ${className}
    `;

    if (product.stock === 0) {
      return `${baseClasses} bg-gray-300 text-gray-500`;
    }

    if (!canAddMore) {
      return `${baseClasses} bg-orange-500 text-white hover:bg-orange-600`;
    }

    if (justAdded) {
      return `${baseClasses} bg-green-500 text-white`;
    }

    if (isAdding) {
      return `${baseClasses} ${variantClasses[variant]} opacity-75`;
    }

    return `${baseClasses} ${variantClasses[variant]}`;
  };

  const getButtonText = () => {
    if (product.stock === 0) {
      return '暂时缺货';
    }

    if (!canAddMore) {
      return '库存不足';
    }

    if (justAdded) {
      return '已添加';
    }

    if (isAdding) {
      return '添加中...';
    }

    return quantity > 1 ? `添加 ${quantity} 件到购物车` : '加入购物车';
  };

  const getIcon = () => {
    if (justAdded) {
      return <CheckIcon className="w-5 h-5 mr-2" />;
    }

    if (isAdding) {
      return (
        <div className="w-5 h-5 mr-2">
          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
        </div>
      );
    }

    return <ShoppingCartIcon className="w-5 h-5 mr-2" />;
  };

  return (
    <button
      onClick={handleAddToCart}
      disabled={product.stock === 0 || isAdding}
      className={getButtonClasses()}
      title={getButtonText()}
    >
      {getIcon()}
      {getButtonText()}
    </button>
  );
};

export default AddToCartButton;
