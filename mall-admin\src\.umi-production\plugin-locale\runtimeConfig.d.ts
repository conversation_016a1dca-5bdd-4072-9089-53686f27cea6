// This file is generated by <PERSON><PERSON> automatically
// DO NOT CHANGE IT MANUALLY!
import {
  IntlCache,
  createIntl,
} from 'C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/react-intl@3.12.1_react@18.3.1/node_modules/react-intl';
type OptionalIntlConfig = Omit<Parameters<typeof createIntl>[0], 'locale' | 'defaultLocale'>;
export interface IRuntimeConfig {
    locale?: {
      getLocale?: () => string;
      cache?: IntlCache;
    } & OptionalIntlConfig;
};
