'use client';

import React from 'react';
import { useCartStore } from '../../store/cartStore';

/**
 * 購物車調試組件
 * 僅在開發環境中顯示購物車狀態信息
 */
export default function CartDebug() {
  const { items, totalItems, totalPrice, isHydrated } = useCartStore();

  // 只在開發環境中顯示
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  // 只在水合完成后显示，避免SSR不匹配
  if (!isHydrated) {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 bg-black bg-opacity-80 text-white p-4 rounded-lg text-xs max-w-xs z-50">
      <h4 className="font-bold mb-2">購物車調試信息</h4>
      <div className="space-y-1">
        <div>水合狀態: ✅ 已完成</div>
        <div>商品數量: {items.length}</div>
        <div>總件數: {totalItems}</div>
        <div>總價格: ¥{totalPrice.toFixed(2)}</div>
        <div>本地存儲: {localStorage.getItem('cart-storage') ? '✅ 存在' : '❌ 不存在'}</div>
      </div>
      {items.length > 0 && (
        <div className="mt-2">
          <div className="font-semibold">商品列表:</div>
          {items.map((item, index) => (
            <div key={item.id} className="text-xs">
              {index + 1}. {item.name} x{item.quantity}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
