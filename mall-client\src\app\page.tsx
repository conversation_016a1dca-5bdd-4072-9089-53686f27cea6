'use client';

import React, { useState, useEffect } from 'react';
import ProductCard from '../components/Product/ProductCard';
import CategoryFilter from '../components/Product/CategoryFilter';

// 定义 Product 接口
interface Product {
  id: number;
  name: string;
  description: string;
  price: number;
  stock: number;
  imageUrl: string;
  categoryId?: number;
}

export default function Home() {
  const [products, setProducts] = useState<Product[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<number | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 获取商品数据
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoading(true);
        console.log('开始获取商品数据...');

        const response = await fetch("http://localhost:8080/api/products", {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          // 添加CORS配置
          mode: 'cors',
        });

        console.log('Response status:', response.status);
        console.log('Response ok:', response.ok);

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('获取到的商品数据:', data);
        setProducts(data);
        setFilteredProducts(data);
      } catch (err: unknown) {
        console.error('获取商品数据失败:', err);
        if (err instanceof Error) {
          setError(`获取商品失败: ${err.message}`);
        } else {
          setError(`获取商品失败: ${String(err)}`);
        }
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, []);

  // 处理分类筛选
  useEffect(() => {
    if (selectedCategory === null) {
      setFilteredProducts(products);
    } else {
      const filtered = products.filter(product => product.categoryId === selectedCategory);
      setFilteredProducts(filtered);
    }
  }, [selectedCategory, products]);

  const handleCategoryChange = (categoryId: number | null) => {
    setSelectedCategory(categoryId);
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">加载中...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-red-500 text-center py-8">
          <p>错误: {error}</p>
          <button
            onClick={() => window.location.reload()}
            className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            重新加载
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* 分類篩選 */}
      <CategoryFilter
        onCategoryChange={handleCategoryChange}
        selectedCategory={selectedCategory}
      />

      {/* 商品列表 */}
      {filteredProducts.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-gray-600">
            {selectedCategory ? '该分类暂无商品' : '暂无商品'}
          </p>
        </div>
      ) : (
        <>
          <div className="mb-4 text-sm text-gray-600">
            共找到 {filteredProducts.length} 件商品
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {filteredProducts.map((product) => (
              <ProductCard key={product.id} product={product} />
            ))}
          </div>
        </>
      )}
    </div>
  );
}
