package io.github.roshad.ecommerce.shipping;

import org.apache.ibatis.annotations.*;
import java.util.List;

@Mapper
public interface ShippingMapper {
    
    @Select("SELECT id, order_id, tracking_number, carrier, shipping_method, " +
            "shipped_at, estimated_delivery, notes, created_at, updated_at " +
            "FROM shipping_info WHERE id = #{id}")
    ShippingInfo findById(Long id);
    
    @Select("SELECT id, order_id, tracking_number, carrier, shipping_method, " +
            "shipped_at, estimated_delivery, notes, created_at, updated_at " +
            "FROM shipping_info WHERE order_id = #{orderId}")
    ShippingInfo findByOrderId(Long orderId);
    
    @Select("SELECT id, order_id, tracking_number, carrier, shipping_method, " +
            "shipped_at, estimated_delivery, notes, created_at, updated_at " +
            "FROM shipping_info WHERE tracking_number = #{trackingNumber}")
    ShippingInfo findByTrackingNumber(String trackingNumber);
    
    @Insert("INSERT INTO shipping_info (order_id, tracking_number, carrier, shipping_method, " +
            "shipped_at, estimated_delivery, notes, created_at, updated_at) " +
            "VALUES (#{orderId}, #{trackingNumber}, #{carrier}, #{shippingMethod}, " +
            "#{shippedAt}, #{estimatedDelivery}, #{notes}, #{createdAt}, #{updatedAt})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(ShippingInfo shippingInfo);
    
    @Update("UPDATE shipping_info SET tracking_number = #{trackingNumber}, " +
            "carrier = #{carrier}, shipping_method = #{shippingMethod}, " +
            "estimated_delivery = #{estimatedDelivery}, notes = #{notes}, " +
            "updated_at = #{updatedAt} WHERE id = #{id}")
    int update(ShippingInfo shippingInfo);
    
    @Delete("DELETE FROM shipping_info WHERE id = #{id}")
    int deleteById(Long id);
    
    @Select("SELECT id, order_id, tracking_number, carrier, shipping_method, " +
            "shipped_at, estimated_delivery, notes, created_at, updated_at " +
            "FROM shipping_info ORDER BY created_at DESC")
    List<ShippingInfo> findAll();
    
    @Select("SELECT id, order_id, tracking_number, carrier, shipping_method, " +
            "shipped_at, estimated_delivery, notes, created_at, updated_at " +
            "FROM shipping_info WHERE carrier = #{carrier} ORDER BY created_at DESC")
    List<ShippingInfo> findByCarrier(String carrier);
}
