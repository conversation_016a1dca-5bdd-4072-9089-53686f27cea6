package io.github.roshad.ecommerce.order;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import java.util.List;

@Service
@RequiredArgsConstructor
public class OrderStatusServiceImpl implements OrderStatusService {
    
    private final OrderStatusMapper orderStatusMapper;
    
    @Override
    public OrderStatus findById(Integer id) {
        return orderStatusMapper.findById(id);
    }
    
    @Override
    public OrderStatus findByStatusName(String statusName) {
        return orderStatusMapper.findByStatusName(statusName);
    }
    
    @Override
    public List<OrderStatus> findAll() {
        return orderStatusMapper.findAll();
    }
    
    @Override
    public OrderStatus createOrderStatus(OrderStatus orderStatus) {
        orderStatusMapper.insert(orderStatus);
        return orderStatus;
    }
    
    @Override
    public void updateOrderStatus(OrderStatus orderStatus) {
        orderStatusMapper.update(orderStatus);
    }
    
    @Override
    public void deleteOrderStatus(Integer id) {
        orderStatusMapper.delete(id);
    }
}
