import React, { useRef, useState } from 'react';
import { ProTable, ProColumns, ActionType, ModalForm, ProFormText, ProFormTextArea, ProFormDigit } from '@ant-design/pro-components';
import { createProduct, updateProduct, deleteProduct, searchProducts } from '@/services/swagger/productController';
import { Button, message, Popconfirm } from 'antd';
import { PlusOutlined } from '@ant-design/icons';

const ProductListPage: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const [createModalVisible, handleCreateModalVisible] = useState<boolean>(false);
  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<API.Product | undefined>(undefined);

  const handleAdd = async (fields: API.Product) => {
    const hide = message.loading('正在添加');
    try {
      await createProduct({ ...fields });
      hide();
      message.success('添加成功');
      return true;
    } catch (error) {
      hide();
      message.error('添加失败，请重试！');
      return false;
    }
  };

  const handleUpdate = async (fields: API.Product) => {
    const hide = message.loading('正在配置');
    try {
      await updateProduct({ id: currentRow?.id as number }, { ...fields });
      hide();
      message.success('配置成功');
      return true;
    } catch (error) {
      hide();
      message.error('配置失败，请重试！');
      return false;
    }
  };

  const handleDelete = async (id: number) => {
    const hide = message.loading('正在删除');
    try {
      await deleteProduct({ id });
      hide();
      message.success('删除成功');
      if (actionRef.current) {
        actionRef.current.reload();
      }
      return true;
    } catch (error) {
      hide();
      message.error('删除失败，请重试！');
      return false;
    }
  };

  const columns: ProColumns<API.Product>[] = [
    {
      title: '商品ID',
      dataIndex: 'id',
      valueType: 'digit',
      hideInSearch: true,
      editable: false, // ID不可编辑
    },
    {
      title: '商品名或描述',
      dataIndex: 'name',
      valueType: 'text',
      formItemProps: {
        rules: [
          {
            required: false, // 搜索字段可以为空
          },
        ],
      },
    },
    {
      title: '描述',
      dataIndex: 'description',
      valueType: 'textarea',
      hideInSearch: true,
    },
    {
      title: '价格',
      dataIndex: 'price',
      valueType: 'money',
      hideInSearch: true,
      sorter: true, // 启用价格排序
    },
    {
      title: '库存',
      dataIndex: 'stock',
      valueType: 'digit',
      hideInSearch: true,
    },
    {
      title: '分类ID',
      dataIndex: 'categoryId',
      valueType: 'digit',
      hideInSearch: true,
    },
    {
      title: '商品图片',
      dataIndex: 'imageUrl',
      valueType: 'image',
      hideInSearch: true,
      render: (_, record) => (record.imageUrl ? <img src={record.imageUrl} alt="商品图片" style={{ width: 50, height: 50 }} /> : null),
    },
    {
      title: '标签',
      dataIndex: 'tags',
      valueType: 'text',
      hideInSearch: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      valueType: 'dateTime',
      hideInSearch: true,
      sorter: true,
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      valueType: 'dateTime',
      hideInSearch: true,
      sorter: true,
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      render: (_, record) => [
        <a
          key="edit"
          onClick={() => {
            handleUpdateModalVisible(true);
            setCurrentRow(record);
          }}
        >
          编辑
        </a>,
        <Popconfirm
          key="delete"
          title="确定删除该商品吗？"
          onConfirm={async () => {
            await handleDelete(record.id as number);
          }}
          okText="是"
          cancelText="否"
        >
          <a>删除</a>
        </Popconfirm>,
      ],
    },
  ];

  return (
    <>
      <ProTable<API.Product>
        headerTitle="商品列表"
        actionRef={actionRef}
        rowKey="id"
        request={async (params, sorter) => {
          const searchParams: API.searchProductsParams = {
            keyword: params.name || '',
          };
          const msg = await searchProducts(searchParams);

          let sortedData = [...msg]; // 复制一份数据进行排序

          // 前端排序逻辑
          if (Object.keys(sorter).length > 0) {
            const sortField = Object.keys(sorter)[0];
            const sortOrder = sorter[sortField];

            sortedData.sort((a, b) => {
              const aValue = a[sortField as keyof API.Product];
              const bValue = b[sortField as keyof API.Product];

              if (typeof aValue === 'string' && typeof bValue === 'string') {
                return sortOrder === 'ascend' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
              }
              if (typeof aValue === 'number' && typeof bValue === 'number') {
                return sortOrder === 'ascend' ? aValue - bValue : bValue - aValue;
              }
              // 对于日期类型，可以转换为时间戳进行比较
              if (sortField === 'createdAt' || sortField === 'updatedAt') {
                const aTime = new Date(aValue as string).getTime();
                const bTime = new Date(bValue as string).getTime();
                return sortOrder === 'ascend' ? aTime - bTime : bTime - aTime;
              }
              return 0;
            });
          }

          return {
            data: sortedData,
            success: true,
            total: sortedData.length,
          };
        }}
        columns={columns}
        search={{
          labelWidth: 'auto',
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        toolBarRender={() => [
          <Button
            key="button"
            icon={<PlusOutlined />}
            type="primary"
            onClick={() => handleCreateModalVisible(true)}
          >
            新建商品
          </Button>,
        ]}
      />

      <ModalForm
        title="新建商品"
        width="400px"
        open={createModalVisible}
        onOpenChange={handleCreateModalVisible}
        onFinish={async (value) => {
          const success = await handleAdd(value as API.Product);
          if (success) {
            handleCreateModalVisible(false);
            if (actionRef.current) {
              actionRef.current.reload();
            }
          }
        }}
      >
        <ProFormText
          rules={[
            {
              required: true,
              message: '商品名称为必填项',
            },
          ]}
          width="md"
          name="name"
          label="商品名称"
        />
        <ProFormTextArea width="md" name="description" label="描述" />
        <ProFormDigit
          rules={[
            {
              required: true,
              message: '价格为必填项',
            },
          ]}
          width="md"
          name="price"
          label="价格"
          min={0}
          fieldProps={{ precision: 2 }}
        />
        <ProFormDigit
          rules={[
            {
              required: true,
              message: '库存为必填项',
            },
          ]}
          width="md"
          name="stock"
          label="库存"
          min={0}
        />
        <ProFormDigit width="md" name="categoryId" label="分类ID" min={0} />
        <ProFormText width="md" name="imageUrl" label="图片URL" />
        <ProFormText width="md" name="tags" label="标签" />
      </ModalForm>

      <ModalForm
        title="编辑商品"
        width="400px"
        open={updateModalVisible}
        onOpenChange={handleUpdateModalVisible}
        initialValues={currentRow}
        onFinish={async (value) => {
          const success = await handleUpdate(value as API.Product);
          if (success) {
            handleUpdateModalVisible(false);
            setCurrentRow(undefined);
            if (actionRef.current) {
              actionRef.current.reload();
            }
          }
        }}
      >
        <ProFormText
          rules={[
            {
              required: true,
              message: '商品名称为必填项',
            },
          ]}
          width="md"
          name="name"
          label="商品名称"
        />
        <ProFormTextArea width="md" name="description" label="描述" />
        <ProFormDigit
          rules={[
            {
              required: true,
              message: '价格为必填项',
            },
          ]}
          width="md"
          name="price"
          label="价格"
          min={0}
          fieldProps={{ precision: 2 }}
        />
        <ProFormDigit
          rules={[
            {
              required: true,
              message: '库存为必填项',
            },
          ]}
          width="md"
          name="stock"
          label="库存"
          min={0}
        />
        <ProFormDigit width="md" name="categoryId" label="分类ID" min={0} />
        <ProFormText width="md" name="imageUrl" label="图片URL" />
        <ProFormText width="md" name="tags" label="标签" />
      </ModalForm>
    </>
  );
};

export default ProductListPage;
