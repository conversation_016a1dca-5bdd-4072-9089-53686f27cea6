// @ts-nocheck
// This file is generated by Umi automatically
// DO NOT CHANGE IT MANUALLY!
// defineApp
export { defineApp } from './core/defineApp'
export type { RuntimeConfig } from './core/defineApp'
// plugins
export { Access, useAccess, useAccessMarkedRoutes } from 'C:/Users/<USER>/Desktop/mall/mall-admin/src/.umi-production/plugin-access';
export { addLocale, setLocale, getLocale, getIntl, useIntl, injectIntl, formatMessage, FormattedMessage, getAllLocales, FormattedDate, FormattedDateParts, FormattedDisplayName, FormattedHTMLMessage, FormattedList, FormattedNumber, FormattedNumberParts, FormattedPlural, FormattedRelativeTime, FormattedTime, FormattedTimeParts, IntlProvider, RawIntlProvider, SelectLang } from 'C:/Users/<USER>/Desktop/mall/mall-admin/src/.umi-production/plugin-locale';
export { Provider, useModel } from 'C:/Users/<USER>/Desktop/mall/mall-admin/src/.umi-production/plugin-model';
export { useRequest, UseRequestProvider, request, getRequestInstance } from 'C:/Users/<USER>/Desktop/mall/mall-admin/src/.umi-production/plugin-request';
// plugins types.d.ts
export * from 'C:/Users/<USER>/Desktop/mall/mall-admin/src/.umi-production/plugin-access/types.d';
export * from 'C:/Users/<USER>/Desktop/mall/mall-admin/src/.umi-production/plugin-antd/types.d';
export * from 'C:/Users/<USER>/Desktop/mall/mall-admin/src/.umi-production/plugin-layout/types.d';
export * from 'C:/Users/<USER>/Desktop/mall/mall-admin/src/.umi-production/plugin-request/types.d';
// @umijs/renderer-*
export { createBrowserHistory, createHashHistory, createMemoryHistory, Helmet, HelmetProvider, createSearchParams, generatePath, matchPath, matchRoutes, Navigate, NavLink, Outlet, resolvePath, useLocation, useMatch, useNavigate, useOutlet, useOutletContext, useParams, useResolvedPath, useRoutes, useSearchParams, useAppData, useClientLoaderData, useLoaderData, useRouteProps, useSelectedRoutes, useServerLoaderData, renderClient, __getRoot, Link, useRouteData, __useFetcher, withRouter } from 'C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+renderer-react@4.4.1_5d37cdc93ae4c557f74cbc834d273583/node_modules/@umijs/renderer-react';
export type { History, ClientLoader } from 'C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+renderer-react@4.4.1_5d37cdc93ae4c557f74cbc834d273583/node_modules/@umijs/renderer-react'
// umi/client/client/plugin
export { ApplyPluginsType, PluginManager } from 'C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/umi@4.4.11_@babel+core@7.4._97aef5f84ba3bed6a7b4ab93e4bba605/node_modules/umi/client/client/plugin.js';
export { history, createHistory } from './core/history';
export { terminal } from './core/terminal';
// react ssr
export const useServerInsertedHTML: Function = () => {};
