# 客户端发货功能移除方案

**问题描述**: 客户端（mall-client）中存在不应该有的发货按钮和发货功能，这些功能应该只在管理后台存在。

**问题分析**:
1. 普通用户（买家）不应该有发货权限
2. 发货功能应该只在管理后台（mall-admin）中存在
3. 客户端的订单页面应该只能查看订单状态，不能进行发货操作

## 解决方案

### 1. 移除普通用户订单页面的发货功能

**文件**: `mall-client/src/app/orders/page.tsx`

**移除的内容**:
- 发货模态框相关状态
- 管理员权限检查
- 发货按钮和相关逻辑
- ShippingModal 组件引用

**保留的功能**:
- 订单列表查看
- 订单状态显示
- 订单详情查看按钮

### 2. 删除不应该存在的组件

**删除文件**: `mall-client/src/components/Order/ShippingModal.tsx`

**原因**: 普通用户不应该有发货功能，此组件应该只在管理后台存在

### 3. 创建管理员专用发货组件

**新建文件**: `mall-client/src/components/Admin/ShippingModal.tsx`

**用途**: 
- 仅供管理员页面使用
- 明确标识为管理员功能
- 与普通用户功能分离

### 4. 更新管理员订单页面

**文件**: `mall-client/src/app/admin/orders/page.tsx`

**更新内容**:
- 使用新的 AdminShippingModal 组件
- 保持发货功能完整性

## 修改详情

### 修改前的问题

```typescript
// 在普通用户订单页面中存在：
const isAdmin = true; // 模拟管理员权限
const canShipOrder = (status: OrderStatus) => { ... };
const openShippingModal = (orderId: number, orderNumber: string) => { ... };

// 发货按钮
{isAdmin && canShipOrder(order.status) && (
  <button onClick={() => openShippingModal(order.id!, order.orderNumber)}>
    发货
  </button>
)}

// 发货模态框
<ShippingModal ... />
```

### 修改后的结果

```typescript
// 普通用户订单页面只保留：
<Link href={`/orders/${order.id}`}>
  <EyeIcon className="w-4 h-4" />
  <span>查看详情</span>
</Link>
```

## 权限分离

### 普通用户（买家）权限
- ✅ 查看自己的订单
- ✅ 查看订单详情
- ✅ 查看订单状态
- ❌ 发货操作
- ❌ 修改订单状态

### 管理员权限
- ✅ 查看所有订单
- ✅ 发货操作
- ✅ 修改订单状态
- ✅ 订单管理

## 文件结构

```
mall-client/
├── src/
│   ├── app/
│   │   ├── orders/page.tsx          # 普通用户订单页面（无发货功能）
│   │   └── admin/
│   │       └── orders/page.tsx      # 管理员订单页面（有发货功能）
│   └── components/
│       ├── Admin/
│       │   └── ShippingModal.tsx    # 管理员专用发货模态框
│       └── Order/
│           └── [其他订单组件]       # 普通用户订单组件
```

## 安全考虑

1. **前端权限控制**: 移除了普通用户界面中的发货功能
2. **后端权限验证**: 后端 API 仍需要 `@PreAuthorize("hasRole('ADMIN')")` 注解
3. **路由保护**: 管理员页面应该有适当的路由保护
4. **用户体验**: 普通用户界面更加简洁，避免混淆

## 测试建议

1. **普通用户测试**:
   - 访问 `/orders` 页面
   - 确认没有发货按钮
   - 确认只能查看订单详情

2. **管理员测试**:
   - 访问 `/admin/orders` 页面
   - 确认发货功能正常
   - 测试发货流程

3. **权限测试**:
   - 确认普通用户无法访问管理员页面
   - 确认后端 API 权限验证正常

## 后续改进建议

1. **路由保护**: 为管理员页面添加身份验证中间件
2. **角色管理**: 完善用户角色和权限系统
3. **UI 优化**: 为管理员和普通用户提供不同的界面主题
4. **审计日志**: 记录管理员操作日志
