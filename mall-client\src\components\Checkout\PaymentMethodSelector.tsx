'use client';

import React from 'react';
import { CheckIcon } from '@heroicons/react/24/outline';
import { PaymentMethod } from '../../types/order';

interface PaymentMethodSelectorProps {
  selectedMethod: PaymentMethod | null;
  onMethodSelect: (method: PaymentMethod) => void;
}

// 支付方式配置
const paymentMethods = [
  {
    id: PaymentMethod.CREDIT_CARD,
    name: '信用卡',
    description: '支持 Visa、MasterCard、銀聯等',
    icon: '💳',
    available: true
  },
  {
    id: PaymentMethod.DEBIT_CARD,
    name: '借記卡',
    description: '支持各大銀行借記卡',
    icon: '🏦',
    available: true
  },
  {
    id: PaymentMethod.ALIPAY,
    name: '支付寶',
    description: '使用支付寶掃碼支付',
    icon: '🅰️',
    available: true
  },
  {
    id: PaymentMethod.WECHAT_PAY,
    name: '微信支付',
    description: '使用微信掃碼支付',
    icon: '💬',
    available: true
  },
  {
    id: PaymentMethod.PAYPAL,
    name: 'PayPal',
    description: '國際支付平台',
    icon: '🅿️',
    available: false // 暫不可用
  },
  {
    id: PaymentMethod.CASH_ON_DELIVERY,
    name: '貨到付款',
    description: '送貨時現金支付',
    icon: '💰',
    available: true
  }
];

const PaymentMethodSelector: React.FC<PaymentMethodSelectorProps> = ({
  selectedMethod,
  onMethodSelect
}) => {
  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">支付方式</h3>
      
      <div className="space-y-3">
        {paymentMethods.map((method) => (
          <div
            key={method.id}
            className={`border rounded-lg p-4 cursor-pointer transition-colors ${
              !method.available
                ? 'border-gray-200 bg-gray-50 cursor-not-allowed opacity-60'
                : selectedMethod === method.id
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 hover:border-gray-300'
            }`}
            onClick={() => method.available && onMethodSelect(method.id)}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <span className="text-2xl">{method.icon}</span>
                <div>
                  <div className="flex items-center space-x-2">
                    <span className="font-medium text-gray-900">
                      {method.name}
                    </span>
                    {!method.available && (
                      <span className="bg-gray-200 text-gray-600 text-xs px-2 py-1 rounded">
                        暫不可用
                      </span>
                    )}
                  </div>
                  <p className="text-gray-600 text-sm">{method.description}</p>
                </div>
              </div>
              
              {selectedMethod === method.id && method.available && (
                <CheckIcon className="w-5 h-5 text-blue-600" />
              )}
            </div>
          </div>
        ))}
      </div>
      
      {/* 支付安全提示 */}
      <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
        <div className="flex items-start space-x-2">
          <span className="text-green-600 text-lg">🔒</span>
          <div>
            <h4 className="text-green-800 font-medium text-sm">安全支付保障</h4>
            <p className="text-green-700 text-xs mt-1">
              我們使用SSL加密技術保護您的支付信息，確保交易安全可靠
            </p>
          </div>
        </div>
      </div>
      
      {/* 支付說明 */}
      {selectedMethod && (
        <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h4 className="text-blue-800 font-medium text-sm mb-2">支付說明</h4>
          <div className="text-blue-700 text-xs space-y-1">
            {selectedMethod === PaymentMethod.CREDIT_CARD && (
              <p>• 支付完成後，我們會立即處理您的订单</p>
            )}
            {selectedMethod === PaymentMethod.ALIPAY && (
              <p>• 點擊確認订单後，將跳轉到支付寶支付頁面</p>
            )}
            {selectedMethod === PaymentMethod.WECHAT_PAY && (
              <p>• 點擊確認订单後，將顯示微信支付二維碼</p>
            )}
            {selectedMethod === PaymentMethod.CASH_ON_DELIVERY && (
              <>
                <p>• 送貨員會在送達時收取現金</p>
                <p>• 請準備好零錢，送貨員可能無法找零</p>
              </>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default PaymentMethodSelector;
