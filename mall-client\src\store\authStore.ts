import { create } from 'zustand';
import { persist } from 'zustand/middleware';

// 用户信息接口
export interface User {
  id: number;
  username: string;
  email: string;
  role: string;
  createdAt: string;
}

// 认证状态接口
interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  
  // 操作方法
  login: (username: string, password: string) => Promise<{ success: boolean; message: string }>;
  register: (username: string, password: string, confirmPassword: string, email: string) => Promise<{ success: boolean; message: string }>;
  logout: () => void;
  checkAuth: () => Promise<void>;
  updateUser: (user: User) => void;
  setLoading: (loading: boolean) => void;
}

// API 基础URL
const API_BASE_URL = 'http://localhost:8080/api/auth';

// 创建认证 store
export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,

      // 登录
      login: async (username: string, password: string) => {
        set({ isLoading: true });
        
        try {
          const response = await fetch(`${API_BASE_URL}/login`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ username, password }),
          });

          const data = await response.json();

          if (data.success) {
            set({
              user: data.user,
              token: data.token,
              isAuthenticated: true,
              isLoading: false,
            });
            return { success: true, message: data.message };
          } else {
            set({ isLoading: false });
            return { success: false, message: data.message };
          }
        } catch (error: unknown) {
          console.error("登录失败:", error);
          set({ isLoading: false });
          return { success: false, message: '登录失败，请检查网络连接' };
        }
      },

      // 注册
      register: async (username: string, password: string, confirmPassword: string, email: string) => {
        set({ isLoading: true });
        
        try {
          const response = await fetch(`${API_BASE_URL}/register`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              username,
              password,
              confirmPassword,
              email,
            }),
          });

          const data = await response.json();

          if (data.success) {
            set({ isLoading: false });
            return { success: true, message: data.message };
          } else {
            set({ isLoading: false });
            return { success: false, message: data.message };
          }
        } catch (error: unknown) {
          console.error("注册失败:", error);
          set({ isLoading: false });
          return { success: false, message: '注册失败，请检查网络连接' };
        }
      },

      // 登出
      logout: () => {
        set({
          user: null,
          token: null,
          isAuthenticated: false,
          isLoading: false,
        });
      },

      // 检查认证状态
      checkAuth: async () => {
        const { token } = get();
        
        if (!token) {
          return;
        }

        set({ isLoading: true });

        try {
          const response = await fetch(`${API_BASE_URL}/profile`, {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json',
            },
          });

          if (response.ok) {
            const data = await response.json();
            if (data.success) {
              set({
                user: data.user,
                isAuthenticated: true,
                isLoading: false,
              });
            } else {
              // Token 无效，清除认证状态
              get().logout();
            }
          } else {
            // Token 无效，清除认证状态
            get().logout();
          }
        } catch (error) {
          console.error('检查认证状态失败:', error);
          get().logout();
        }
      },

      // 更新用户信息
      updateUser: (user: User) => {
        set({ user });
      },

      // 设置加载状态
      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },
    }),
    {
      name: 'auth-storage', // 本地存储的key
      // 只持久化必要的字段
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);

// 获取认证头部
export const getAuthHeaders = () => {
  const token = useAuthStore.getState().token;
  return token ? { Authorization: `Bearer ${token}` } : {};
};
